[gd_scene load_steps=3 format=3 uid="uid://bfqsagv3ntwky"]

[ext_resource type="Script" uid="uid://clkh0mb6118au" path="res://UI/Components/selection_info_box.gd" id="1_b1bvm"]
[ext_resource type="PackedScene" uid="uid://fq6jqadjsv86" path="res://UI/Components/emphasis_label.tscn" id="2_5dgg1"]

[node name="SelectionBoxInfo" type="PanelContainer" node_paths=PackedStringArray("building_name_label", "production_time_label", "costs_label")]
offset_right = 156.0
offset_bottom = 87.0
script = ExtResource("1_b1bvm")
building_name_label = NodePath("MarginContainer/Info Container/Building Name")
production_time_label = NodePath("MarginContainer/Info Container/Production Time Container/Production Time")
costs_label = NodePath("MarginContainer/Info Container/Costs Container/Costs")

[node name="MarginContainer" type="MarginContainer" parent="."]
layout_mode = 2
theme_override_constants/margin_left = 5
theme_override_constants/margin_top = 5
theme_override_constants/margin_right = 5
theme_override_constants/margin_bottom = 5

[node name="Info Container" type="VBoxContainer" parent="MarginContainer"]
layout_mode = 2

[node name="Building Name" parent="MarginContainer/Info Container" instance=ExtResource("2_5dgg1")]
layout_mode = 2

[node name="Production Time Container" type="HBoxContainer" parent="MarginContainer/Info Container"]
layout_mode = 2

[node name="Production Time Header" type="Label" parent="MarginContainer/Info Container/Production Time Container"]
layout_mode = 2
text = "Production Time:"

[node name="Production Time" parent="MarginContainer/Info Container/Production Time Container" instance=ExtResource("2_5dgg1")]
layout_mode = 2
text = "0"

[node name="Costs Container" type="HBoxContainer" parent="MarginContainer/Info Container"]
layout_mode = 2

[node name="Costs Header" type="Label" parent="MarginContainer/Info Container/Costs Container"]
layout_mode = 2
text = "Costs:"

[node name="Costs" parent="MarginContainer/Info Container/Costs Container" instance=ExtResource("2_5dgg1")]
layout_mode = 2
text = "0"

[connection signal="mouse_exited" from="." to="." method="_on_mouse_exited"]
