class_name ResearchNode
extends UnlockableNode


@export var recipes_to_unlock: Array[ItemRecipe]
@export var buildings_to_unlock: Array[BuildingStats]


func _ready() -> void:
	super._ready()
	_create_links()


func _input_event(_viewport: Viewport, event: InputEvent, _shape_idx: int) -> void:
	if event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_LEFT and event.pressed:
		_handle_input()


func _handle_input() -> void:
	if NodeState.Enum.UNLOCKED == node_state:
		UnlockManager.unlock_item_recipes(recipes_to_unlock)
		UnlockManager.unlock_buildings(buildings_to_unlock)
		
		set_state(NodeState.Enum.BEATEN)


func _on_unlock_button_pressed() -> void:
	if NodeState.Enum.UNLOCKED == node_state:
		$BasicInfo.show_hide()
	_handle_input()
