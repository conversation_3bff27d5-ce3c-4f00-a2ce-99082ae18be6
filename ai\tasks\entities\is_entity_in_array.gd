@tool
extends BTCondition

@export var target_var := &"selected_entity"
@export var entities_var := &"entities"


func _generate_name() -> String:
	return "Check if selected %s is in %s" % [target_var, entities_var]


func _tick(_delta: float) -> Status:
	var entity   = blackboard.get_var(target_var, null, false)
	var entities = blackboard.get_var(entities_var, null, false)
	if entity == null:
		return FAILURE

	if entities == null:
		return FAILURE

	if entities.has(entity):
		return SUCCESS

	return FAILURE
