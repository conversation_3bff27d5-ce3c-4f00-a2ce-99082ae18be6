extends Node2D

const BUILDINGS_NODE_LOCATION: String = '/root/PlanetSurface/Buildings'
@onready var TEMPLATE_BUILDING: PackedScene = load("res://Entities/Buildings/building base/building_template.tscn")
@onready var tilemap: TileMapLayer = $"../Tiles/Ground"
@onready var level_manager: LevelManager = get_parent() as LevelManager


@warning_ignore("unused_signal")
signal building_built(where: Vector2i, dimensions: Vector2i)
signal building_blocked(what: String, why: String)
signal building_mode_cancelled


var is_dragging: bool = false
var start_building_tile: Vector2i = Vector2i.ZERO
var last_mouse_tile: Vector2i = Vector2i.ZERO
var new_mouse_tile: Vector2i = Vector2i.ZERO
var direction_vector: Vector2i = Vector2i.ZERO


func _building_scaling(building_prefab: Node) -> void:
	var tile_size = tilemap.tile_set.tile_size
	var collider: CollisionShape2D = building_prefab.find_child("CollisionShape2D", true, false)

	# Ensure the shape is initialized issue when instancing even if shape is assigned in inspector
	if collider.shape == null:
		collider.shape = RectangleShape2D.new()

	# Check type and set size
	if collider.shape is RectangleShape2D:
		var shape: RectangleShape2D = collider.shape
		shape.size = BuildingModeManager.selected_building_stats.dimensions * tile_size
		
	else:
		print("⚠️ Shape is not RectangleShape2D: ", collider.shape)


func _cancel_building():
	is_dragging = false
	StateManager.state = StateManager.States.STATE_PLAY
	BuildingModeManager.building_rotation = 0
	building_mode_cancelled.emit()


func _build_building_at_position(world_position: Vector2i):
	var tilemap_position: Vector2i = tilemap.local_to_map(world_position)

	if BuildingModeManager.is_building_site_occupied(tilemap_position):
		building_blocked.emit(
			BuildingModeManager.selected_building_stats.resource_name,
			'Something is already built there!'
		)
		return

	var building_cost = BuildingModeManager.selected_building_stats.cost
	print('building cost ', building_cost)
	if not level_manager.inventory.sufficient_resources(building_cost):
		return
	level_manager.inventory.consume_resources(building_cost)

	
	var instance: Building = TEMPLATE_BUILDING.instantiate().initialize_building()
	var buildings_node: Node2D = get_node(BUILDINGS_NODE_LOCATION)
	instance.reparent(buildings_node)
	instance.owner = get_tree().current_scene
	
	
	#Take into account building dimensions offset
	if BuildingModeManager.selected_building_stats.dimensions != Vector2i.ONE:
		var offset = tilemap.tile_set.tile_size/4*BuildingModeManager.selected_building_stats.dimensions
		world_position+=offset
	instance.position = world_position
	instance.tile_coordinates = tilemap_position

	# Handling building rotation
	_sync_rotation(instance)
	# Handling animations and syncing them
	BuildingModeManager._sync_animation(instance, false)

	_building_scaling(instance)
	AudioManager.create_audio(SoundEffect.SOUND_EFFECT_TYPE.BUILDING_PLACED)
	set_building_site_as_occupied(tilemap_position,instance)
	# instance.initialize_output_coordinates()
	instance.on_build()
	BuildingSignalBus.building_built.emit(instance)
	print('occupied tiles ', BuildingModeManager.occupied_tiles)


func _sync_rotation(instance:Node2D) -> void:
	if not BuildingModeManager.selected_building_stats.is_rotateable:
		return
		
	#NOTE one needs rotation as an int other as float
	var _rotation: int = BuildingModeManager.building_rotation
	if BuildingModeManager.selected_building_stats.building_type == BuildingType.Enum.CONVEYOR_BELT:
		if start_building_tile != new_mouse_tile:
			_rotation = vector_to_degrees(direction_vector)
	
	BuildingModeManager.building_rotation = _rotation
	instance.rotation_degrees = float(_rotation)

#
#func _sync_animation(animations: AnimatedSprite2D) -> void:
	#if BuildingModeManager.selected_building_stats.animation_frames:
		#animations.play(
			#"East" if BuildingModeManager.selected_building_stats.is_rotateable else "North"
		#)
		#var sync_node = BuildingModeManager.sync.get_child(0)
		#animations.set_frame_and_progress(sync_node.frame, sync_node.frame_progress)


func vector_to_degrees(direction: Vector2) -> int:
	match direction:
		Vector2(1, 0): return 0    # East
		Vector2(0, -1): return 270  # North
		Vector2(-1, 0): return 180 # West
		Vector2(0, 1): return 90  # South
		_:
			push_error("Invalid cardinal direction vector: %s" % direction)
			return 0


func set_building_site_as_occupied(tilemap_position: Vector2i, node: Node2D) -> void:
	var building_dimensions: Vector2i = BuildingModeManager.selected_building_stats.dimensions
	#var world_position: Vector2 = tilemap.map_to_local(tilemap_position)

	for x_offset in range(building_dimensions.x):
		for y_offset in range(building_dimensions.y):
			BuildingModeManager.occupied_tiles[tilemap_position + Vector2i(x_offset, y_offset)] = node


func _on_hud_caught_input(event: InputEvent) -> void:
	if StateManager.state != StateManager.States.STATE_BUILD:
		return
	if event is InputEventMouseButton:
		if event.button_index == MOUSE_BUTTON_LEFT:
			var local_mouse_pos: Vector2 = to_local(get_global_mouse_position())

			if event.is_pressed():
				start_building_tile = tilemap.local_to_map(local_mouse_pos)
				last_mouse_tile = tilemap.local_to_map(local_mouse_pos)
				is_dragging = true

			if event.is_released():
				_build_building_at_position(tilemap.map_to_local(last_mouse_tile))
				is_dragging = false

		if event.button_index == MOUSE_BUTTON_RIGHT:
			_cancel_building()


func _process(_delta):
	if not is_dragging:
		return

	var local_mouse_pos = to_local(get_global_mouse_position())
	new_mouse_tile = tilemap.local_to_map(local_mouse_pos)
	
	if new_mouse_tile == last_mouse_tile:
		return
	
	direction_vector = new_mouse_tile-last_mouse_tile
	
	if new_mouse_tile in _get_cardinal_directions(last_mouse_tile):
		_build_building_at_position(tilemap.map_to_local(last_mouse_tile))
		last_mouse_tile = new_mouse_tile
		
	else:
		var path=_get_simple_path(last_mouse_tile,new_mouse_tile)
		#print('making path from ', last_mouse_tile,' to ',new_mouse_tile )
		#print('path ',path)
		#print('path length ', path.size())
		
		#path gets constructed to the last but one tile so 2 different 
		#paths could be connected by the default logic
		for tile in range(path.size()-1):
			last_mouse_tile = path[tile]
			new_mouse_tile = path[tile+1]
			direction_vector=new_mouse_tile-last_mouse_tile
			_build_building_at_position(tilemap.map_to_local(last_mouse_tile))
			last_mouse_tile = new_mouse_tile


func _get_simple_path(start: Vector2i, end: Vector2i) -> Array[Vector2i]:
	var path: Array[Vector2i] = []
	path.append(start)
	#get for x
	while start.x != end.x:
		start.x+= 1 if end.x>start.x else -1
		path.append(start)
	
	#get for y
	while start.y != end.y:
		start.y+= 1 if end.y>start.y else -1
		path.append(start)

	return path


const CARDINAL_VECTORS = [
	Vector2i(1,0),
	Vector2i(-1,0),
	Vector2i(0,1),
	Vector2i(0,-1)
]


func _get_cardinal_directions(tile_position : Vector2i) -> Array[Vector2i]:
	var tile_array : Array[Vector2i] = []
	for direction in CARDINAL_VECTORS:
		tile_array.append(tile_position + direction)
	return tile_array


func _unhandled_key_input(_event: InputEvent) -> void:
	if StateManager.state != StateManager.States.STATE_BUILD:
		return
	if Input.is_action_just_pressed("rotate_left"):
		BuildingModeManager.building_rotation -= 90
	if Input.is_action_just_pressed("rotate_right"):
		BuildingModeManager.building_rotation += 90
	if Input.is_key_pressed(KEY_DELETE):
		BuildingModeManager.occupied_tiles.clear()
		while get_node(BUILDINGS_NODE_LOCATION).get_child(0) != null:
			get_node(BUILDINGS_NODE_LOCATION).get_child(0).free()
		
