[gd_scene load_steps=22 format=3 uid="uid://cgbwqppo2n3ae"]

[ext_resource type="Texture2D" uid="uid://bldqbfanyjuoc" path="res://Assets/IconGodotNode/control/icon_door.png" id="1_bg15p"]
[ext_resource type="Script" uid="uid://bm7w8aodi6hhw" path="res://Entities/Enemy/hive.gd" id="1_cr1s7"]
[ext_resource type="PackedScene" uid="uid://de74akkj4ypnl" path="res://Entities/Enemy/Enemy.tscn" id="2_bigkk"]
[ext_resource type="Script" uid="uid://cvsoda347ktvf" path="res://ai/tasks/detection/get_far_entities.gd" id="2_dgjeh"]
[ext_resource type="Script" uid="uid://crlg0rjvgpxpg" path="res://ai/tasks/detection/get_near_entities.gd" id="3_wf76q"]
[ext_resource type="PackedScene" uid="uid://dhvj5lqfiuttb" path="res://Entities/Components/hit_box_component.tscn" id="4_b46c4"]
[ext_resource type="PackedScene" uid="uid://cvogo0j7ferfa" path="res://Entities/Components/health_component.tscn" id="5_t6b16"]
[ext_resource type="PackedScene" uid="uid://sgsrhtv7bywd" path="res://Entities/Components/team_component.tscn" id="6_bjr5w"]
[ext_resource type="PackedScene" uid="uid://t0huyrlmq45w" path="res://Entities/Components/spawner_component.tscn" id="6_c4fns"]
[ext_resource type="PackedScene" uid="uid://d1thcq78aauo5" path="res://Entities/Components/AI/detection_coponent.tscn" id="7_c4fns"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_v1uik"]
prefetch_nodepath_vars = false
var/hive_entities_near/name = &"hive_entities_near"
var/hive_entities_near/type = 28
var/hive_entities_near/value = []
var/hive_entities_near/hint = 0
var/hive_entities_near/hint_string = ""
var/hive_entities_far/name = &"hive_entities_far"
var/hive_entities_far/type = 28
var/hive_entities_far/value = []
var/hive_entities_far/hint = 0
var/hive_entities_far/hint_string = ""

[sub_resource type="BTAction" id="BTAction_t7wwt"]
script = ExtResource("2_dgjeh")
entities_var = &"hive_entities_far"

[sub_resource type="BTAction" id="BTAction_4dncc"]
script = ExtResource("3_wf76q")
entities_var = &"hive_entities_near"

[sub_resource type="BTSequence" id="BTSequence_hgrpk"]
children = [SubResource("BTAction_t7wwt"), SubResource("BTAction_4dncc")]

[sub_resource type="BTSelector" id="BTSelector_v1uik"]
children = [SubResource("BTSequence_hgrpk")]

[sub_resource type="BehaviorTree" id="BehaviorTree_51vtx"]
blackboard_plan = SubResource("BlackboardPlan_v1uik")
root_task = SubResource("BTSelector_v1uik")

[sub_resource type="BlackboardPlan" id="BlackboardPlan_t7wwt"]
prefetch_nodepath_vars = false

[sub_resource type="CircleShape2D" id="CircleShape2D_t6b16"]

[sub_resource type="CircleShape2D" id="CircleShape2D_vdfk8"]

[sub_resource type="CircleShape2D" id="CircleShape2D_dgjeh"]
radius = 71.0282

[sub_resource type="CircleShape2D" id="CircleShape2D_v1uik"]
radius = 116.004

[node name="Hive" type="StaticBody2D" groups=["Entity"]]
script = ExtResource("1_cr1s7")

[node name="BTPlayer" type="BTPlayer" parent="."]
behavior_tree = SubResource("BehaviorTree_51vtx")
blackboard_plan = SubResource("BlackboardPlan_t7wwt")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_t6b16")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("1_bg15p")

[node name="HitBoxComponent" parent="." instance=ExtResource("4_b46c4")]

[node name="CollisionShape2D" type="CollisionShape2D" parent="HitBoxComponent"]
shape = SubResource("CircleShape2D_vdfk8")

[node name="HealthComponent" parent="." instance=ExtResource("5_t6b16")]
position = Vector2(0, -12)

[node name="TeamComponent" parent="." instance=ExtResource("6_bjr5w")]
team = 2

[node name="DetectionComponent" parent="." node_paths=PackedStringArray("nearArea", "farArea") instance=ExtResource("7_c4fns")]
nearArea = NodePath("Inner")
farArea = NodePath("Outer")

[node name="Inner" type="Area2D" parent="DetectionComponent"]
collision_layer = 0
collision_mask = 3

[node name="CollisionShape2D" type="CollisionShape2D" parent="DetectionComponent/Inner"]
shape = SubResource("CircleShape2D_dgjeh")
debug_color = Color(1, 0.5, 0, 0.1)

[node name="Outer" type="Area2D" parent="DetectionComponent"]
collision_layer = 0
collision_mask = 3

[node name="CollisionShape2D" type="CollisionShape2D" parent="DetectionComponent/Outer"]
shape = SubResource("CircleShape2D_v1uik")
debug_color = Color(0.2, 1, 0, 0.1)

[node name="SpawnerComponent" parent="." instance=ExtResource("6_c4fns")]
unit = ExtResource("2_bigkk")

[connection signal="unit_spawned" from="SpawnerComponent" to="." method="_on_spawner_component_unit_spawned"]
