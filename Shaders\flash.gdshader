shader_type canvas_item;

uniform vec4 flash_color : source_color = vec4(1,1,1,1);
uniform float flash_value = 0;

void vertex() {
	// Called for every vertex the material is visible on.
}

void fragment() {
	// Called for every pixel the material is visible on.
	vec4 sprite_color = texture(TEXTURE, UV);
	vec3 final_rgb = mix(sprite_color.rgb, flash_color.rgb, flash_value);
	COLOR = vec4(final_rgb, sprite_color.a);
}