extends Node

const SAVE_ROOT = "res://saves"


signal global_data_changed
signal scene_data_changed


var global_data: Dictionary = {}
var scene_data: Dictionary = {}


var selected_save_name: String:
	set(value):
		if selected_save_name != value:
			# Invalidate scene data
			selected_scene_name = ""

			selected_save_name = value
			
			if not selected_save_name.is_empty():
				load_global_data()
			else:
				# Invalidate global data
				global_data = {}

var selected_scene_name: String:
	set(value):
		if selected_scene_name != value:
			# save old data
			if not selected_scene_name.is_empty():
				save_scene_data()
				save_global_data()

			selected_scene_name = value

			if not selected_scene_name.is_empty():
				load_scene_data()
			else:
				# Invalidate scene data
				scene_data = {}

func get_save_path(save_name: String) -> String:
	if save_name.is_empty():
		return ""
	return "%s/%s" % [SAVE_ROOT, save_name]

func has_save(save_name: String) -> bool:
	if save_name.is_empty():
		return false
	return DirAccess.dir_exists_absolute(get_save_path(save_name))

func get_global_data_file(save_name: String) -> String:
	var save_path = get_save_path(save_name)
	if save_path.is_empty():
		return ""
	return "%s/global_data.json" % save_path

func has_global_data_file(save_name: String) -> bool:
	var save_path = get_save_path(save_name)
	if save_path.is_empty():
		return false
	return FileAccess.file_exists(get_global_data_file(save_name))

func get_scene_data_file(save_name: String, scene_name: String) -> String:
	var save_path = get_save_path(save_name)
	if save_path.is_empty():
		return ""
	return "%s/%s.json" % [save_path, scene_name]

func has_scene_data_file(save_name: String, scene_name: String) -> bool:
	var save_path = get_save_path(save_name)
	if save_path.is_empty():
		return false
	return FileAccess.file_exists(get_scene_data_file(save_name, scene_name))


## Load global data (research, unlocks, etc.)
func load_global_data() -> void:
	global_data = {}
	
	if not has_global_data_file(selected_save_name):
		print("global file [%s] does not exist." % get_global_data_file(selected_save_name))
		return

	var file = FileAccess.open(get_global_data_file(selected_save_name), FileAccess.READ)
	if file == null:
		push_error("Failed to open global file: %s" % get_global_data_file(selected_save_name))
		return

	var json_string = file.get_as_text()
	file.close()

	var json = JSON.new()
	var parse_result = json.parse(json_string)
	if parse_result != OK:
		push_error("Failed to parse global JSON: %s" % json.get_error_message())
		return

	global_data = json.data
	print("Loaded global data: [%s]" % get_global_data_file(selected_save_name))
	global_data_changed.emit()


## Save global data (research, unlocks, etc.)
func save_global_data() -> void:
	DirAccess.make_dir_recursive_absolute(get_save_path(selected_save_name))

	var json_string = JSON.stringify(global_data, "\t")
	var file = FileAccess.open(get_global_data_file(selected_save_name), FileAccess.WRITE)
	if file == null:
		push_error("Failed to create global file: %s" % get_global_data_file(selected_save_name))
		return

	print("Saved global data: [%s]" % get_global_data_file(selected_save_name))
	file.store_string(json_string)
	file.close()


func get_global_data(node: Node) -> Dictionary:
	return global_data.get_or_add(get_component_save_name(node), {})


func load_scene_data() -> void:
	scene_data = {}

	if not has_scene_data_file(selected_save_name, selected_scene_name):
		print("scene file [%s] does not exist." % get_scene_data_file(selected_save_name, selected_scene_name))
		return

	var file = FileAccess.open(get_scene_data_file(selected_save_name, selected_scene_name), FileAccess.READ)
	if file == null:
		push_error("Failed to open scene file: %s" % get_scene_data_file(selected_save_name, selected_scene_name))
		return

	var json_string = file.get_as_text()
	file.close()

	var json = JSON.new()
	var parse_result = json.parse(json_string)
	if parse_result != OK:
		push_error("Failed to parse scene JSON: %s" % json.get_error_message())
		return

	scene_data = json.data
	print("Loaded scene data: [%s]" % get_scene_data_file(selected_save_name, selected_scene_name))
	scene_data_changed.emit()


func save_scene_data() -> void:
	DirAccess.make_dir_recursive_absolute(get_save_path(selected_save_name))

	var json_string = JSON.stringify(scene_data, "\t")
	var file = FileAccess.open(get_scene_data_file(selected_save_name, selected_scene_name), FileAccess.WRITE)
	if file == null:
		push_error("Failed to create scene file: %s" % get_scene_data_file(selected_save_name, selected_scene_name))
		return

	print("Saved scene data: [%s]" % get_scene_data_file(selected_save_name, selected_scene_name))
	file.store_string(json_string)
	file.close()


func get_scene_data(node: Node) -> Dictionary:
	return scene_data.get_or_add(get_component_save_name(node), {})


## Generate a unique save name for a component
func get_component_save_name(node: Node) -> String:
	var component_name: String
	if node.get_script():
		component_name = node.get_script().get_global_name()
		if component_name.is_empty():
			component_name = node.get_script().resource_path.get_file().get_basename()
	else:
		component_name = node.get_class()

	var node_path = str(node.get_path())
	return "%s_%s" % [component_name, node_path.hash()]
