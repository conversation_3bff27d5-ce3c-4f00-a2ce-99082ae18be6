class_name ItemType

enum Enum {
	CIRCUIT_BOARD,
	COAL,
	COPPER_INGOT,
	COPPER_ORE,
	COPPER_WIRE,
	IRON_INGOT,
	IRON_ORE,
	IRON_PLATE,
	IRON_RODS,
	MAKESHIFT_ROCKET,
	ROBOT,
	SAND,
	S<PERSON><PERSON><PERSON>
}


static func to_name(value: Enum) -> StringName:
	for key in Enum:
		if Enum[key] == value:
			return key
	return str(value)


static func is_storable(item: Enum)-> bool:
	return item in [
		Enum.IRON_INGOT,
		Enum.COPPER_INGOT
	]


static func get_storable_items() -> Array[Enum]:
	var storable: Array[Enum]
	for item in Enum.values():
		if is_storable(item):
			storable.append(item)
	return storable
