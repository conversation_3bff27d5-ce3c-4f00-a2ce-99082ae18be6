extends Panel

var X_offset          := 300
var Y_offset          := 150
var move_speed := 25.0 # pixels per second
var original_position := Vector2.ZERO
var target_offset     := Vector2.ZERO
var current_offset    := Vector2.ZERO

# List of preloaded textures
var textures = [
			   preload("res://Assets/Sprites/Backgorund.png"),
			   preload("res://Assets/Sprites/Backgoundblurred.png")
			   ]

var current_texture_index = -1

@onready var tex_a = $TextureRectA
@onready var tex_b = $TextureRectB

var tween: Tween = null


func _ready() -> void:
	original_position = position

	# Initialize first image WITHOUT fading
	current_texture_index = randi() % textures.size()
	tex_a.texture = textures[current_texture_index]
	tex_a.self_modulate.a = 1.0
	tex_b.self_modulate.a = 0.0

	set_new_target(true)



func _process(delta: float) -> void:
	var target_position = original_position + target_offset
	var direction       = target_position - position

	if direction.length() > 0.1:
		var movement = direction.normalized() * move_speed * delta
		if movement.length() > direction.length():
			position = target_position
		else:
			position += movement
	else:
		set_new_target()



func set_new_target(first_time := false) -> void:
	# Choose new target offset
	if current_offset == Vector2.ZERO:
		current_offset = Vector2(
			X_offset * (1 if randf() < 0.5 else -1),
			Y_offset * (1 if randf() < 0.5 else -1)
		)
		position = original_position + current_offset

	target_offset = Vector2(
		X_offset * (-1 if current_offset.x > 0 else 1),
		Y_offset * (-1 if current_offset.y > 0 else 1)
	)
	current_offset = target_offset

	if first_time:
		# Skip texture switch on first initialization
		return

	# Choose a random different texture index
	var new_index = current_texture_index
	while new_index == current_texture_index:
		new_index = randi() % textures.size()
	current_texture_index = new_index

	# Start fade transition to the new texture
	crossfade_to_texture(textures[current_texture_index])



func crossfade_to_texture(new_texture: Texture2D) -> void:
	# Swap TextureRects: tex_b becomes new image on top, fades in
	tex_b.texture = new_texture
	tex_b.self_modulate.a = 0.0

	# Cancel any previous tweens
	if tween:
		tween.kill()

	tween = create_tween().set_parallel(true)
	tween.tween_property(tex_b, "self_modulate:a", 1.0, 1.0)
	tween.tween_property(tex_a, "self_modulate:a", 0.0, 1.0)

	# Swap tex_a and tex_b after fade to maintain layering
	tween.tween_callback(Callable(self, "_swap_textures"))



func _swap_textures() -> void:
	# Swap roles
	var temp = tex_a
	tex_a = tex_b
	tex_b = temp
