extends Node2D

const TRANSPARENT_RED := Color(Color.RED, 0.5)
const TRANSPARENT_GREEN := Color(Color.GREEN, 0.5)

@onready var tilemap: TileMapLayer = $"../Tiles/Ground"
@onready var sprite: Sprite2D = $Sprite2D

var handle_ghost: bool = false
var ghost: Node = null


func _ready() -> void:
	BuildingSignalBus.building_mode_triggered.connect(_ghost_activate)
	BuildingSignalBus.demolition_mode_triggered.connect(_ghost_activate)


func _ghost_activate():
	_update_position_and_rotation()
	_update_color()
	sprite.texture = BuildingModeManager.selected_building_stats.texture
	show()
	set_process(true)


func _ghost_deactivate():
	hide()
	set_process(false)


func _update_position_and_rotation():
	position = tilemap.map_to_local(tilemap.local_to_map(get_global_mouse_position()))
	if BuildingModeManager.selected_building_stats.dimensions != Vector2i.ONE:
		var offset: Vector2 = tilemap.tile_set.tile_size/4*BuildingModeManager.selected_building_stats.dimensions
		position+=offset
	if BuildingModeManager.selected_building_stats.is_rotateable:
		rotation_degrees = BuildingModeManager.building_rotation
	else:
		rotation_degrees = 0


func _update_color():
	var tilemap_position: Vector2i = tilemap.local_to_map(get_global_mouse_position())


	if BuildingModeManager.is_building_site_occupied(tilemap_position):
		sprite.modulate = TRANSPARENT_RED
		return

	sprite.modulate = TRANSPARENT_GREEN


func _process(_delta: float) -> void:
	if StateManager.state == StateManager.States.STATE_BUILD or StateManager.state == StateManager.States.STATE_DEMOLISH:
		_update_position_and_rotation()
		_update_color() 


func _on_demolisher_demolition_mode_cancelled() -> void:
	_ghost_deactivate()


func _on_builder_building_mode_cancelled() -> void:
	_ghost_deactivate()
