[gd_scene load_steps=14 format=3 uid="uid://0ihbkwcfs6yp"]

[ext_resource type="PackedScene" uid="uid://dsyrj2krswx6n" path="res://Scenes/tiles.tscn" id="1_halxe"]
[ext_resource type="Script" uid="uid://d24nynk8yp0ua" path="res://Managers/level_manager.gd" id="1_m78lr"]
[ext_resource type="Script" uid="uid://d2s7r1nqci5th" path="res://Entities/NodeMap/space_camera.gd" id="2_rqvqv"]
[ext_resource type="PackedScene" uid="uid://cc34idqq1k5jk" path="res://UI/ResultsUI.tscn" id="3_2yube"]
[ext_resource type="PackedScene" uid="uid://oaugs621o01i" path="res://UI/HUD.tscn" id="3_x330c"]
[ext_resource type="Script" uid="uid://f7fqo8cjpp3s" path="res://Scripts/builder.gd" id="4_rqvqv"]
[ext_resource type="PackedScene" uid="uid://cdiynhno0rg0o" path="res://Scenes/Menus/PauseMenu.tscn" id="4_yv1m4"]
[ext_resource type="Script" uid="uid://cfad0l6rx7cdc" path="res://Scripts/building_ghost.gd" id="5_x330c"]
[ext_resource type="Script" uid="uid://cgd2bow12o24m" path="res://Scripts/demolisher.gd" id="6_gxwqq"]
[ext_resource type="PackedScene" uid="uid://c5dgr7sr43otq" path="res://Entities/Buildings/Power Network/testing/testing_power_generator.tscn" id="9_11kv8"]
[ext_resource type="PackedScene" uid="uid://ms0scm876sot" path="res://Entities/Buildings/Power Network/power_network.tscn" id="10_4qiat"]
[ext_resource type="PackedScene" uid="uid://c5hbk2wlqmp0g" path="res://Entities/Buildings/Power Network/testing/testing_power_battery.tscn" id="11_gy7vv"]
[ext_resource type="Script" uid="uid://dq2b03205hb1p" path="res://Scripts/level_inventory.gd" id="12_virxb"]

[node name="PlanetSurface" type="Node2D"]
script = ExtResource("1_m78lr")

[node name="Camera2D" type="Camera2D" parent="." node_paths=PackedStringArray("border")]
script = ExtResource("2_rqvqv")
border = NodePath("../CameraLimiter")

[node name="UI" type="CanvasLayer" parent="Camera2D"]

[node name="HUD" parent="Camera2D/UI" instance=ExtResource("3_x330c")]
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="ResultsUi" parent="Camera2D/UI" instance=ExtResource("3_2yube")]

[node name="PauseMenu" parent="Camera2D/UI" instance=ExtResource("4_yv1m4")]

[node name="CameraLimiter" type="ReferenceRect" parent="."]
offset_left = -2179.0
offset_top = -1024.0
offset_right = 1875.0
offset_bottom = 2009.0
mouse_filter = 1
editor_only = false

[node name="Tiles" parent="." instance=ExtResource("1_halxe")]

[node name="Buildings" type="Node2D" parent="."]

[node name="Demolisher" type="Node2D" parent="."]
script = ExtResource("6_gxwqq")

[node name="BuildingGhost" type="Node2D" parent="."]
z_index = 7
z_as_relative = false
script = ExtResource("5_x330c")

[node name="Sprite2D" type="Sprite2D" parent="BuildingGhost"]

[node name="Builder" type="Node2D" parent="."]
script = ExtResource("4_rqvqv")

[node name="Testing Power Grid" type="Node2D" parent="."]

[node name="PowerNetwork" parent="Testing Power Grid" instance=ExtResource("10_4qiat")]
position = Vector2(-281, 18)

[node name="TestingPowerBattery" parent="Testing Power Grid" instance=ExtResource("11_gy7vv")]
position = Vector2(-290, -105)

[node name="TestingPowerGenerator" parent="Testing Power Grid" instance=ExtResource("9_11kv8")]
position = Vector2(-503, 51)

[node name="TestingPowerGenerator2" parent="Testing Power Grid" instance=ExtResource("9_11kv8")]
position = Vector2(-507, 147)

[node name="ResourceInventory" type="Node2D" parent="."]
script = ExtResource("12_virxb")

[connection signal="caught_input" from="Camera2D/UI/HUD" to="Demolisher" method="_on_hud_caught_input"]
[connection signal="caught_input" from="Camera2D/UI/HUD" to="Builder" method="_on_hud_caught_input"]
[connection signal="demolition_mode_cancelled" from="Demolisher" to="BuildingGhost" method="_on_demolisher_demolition_mode_cancelled"]
[connection signal="building_mode_cancelled" from="Builder" to="BuildingGhost" method="_on_builder_building_mode_cancelled"]
[connection signal="building_mode_cancelled" from="Builder" to="BuildingGhost" method="_on_node_2d_building_mode_cancelled"]
