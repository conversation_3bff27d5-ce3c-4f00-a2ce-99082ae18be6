[gd_resource type="Resource" script_class="BuildingStats" load_steps=3 format=3 uid="uid://dol7po7t6ags4"]

[ext_resource type="Script" uid="uid://c7s4elarc1emd" path="res://Entities/Buildings/building base/building_stats.gd" id="1_3q4sd"]
[ext_resource type="Texture2D" uid="uid://dw1xqrdodl3v1" path="res://Assets/Sprites/32x32/DeleteSmall.png" id="2_62mjm"]

[resource]
resource_name = "Demolition Placeholder"
script = ExtResource("1_3q4sd")
translation_key = ""
dimensions = Vector2i(1, 1)
is_rotateable = false
building_type = 0
component_scripts = Array[Script]([])
texture = ExtResource("2_62mjm")
unlocked_by_default = false
menu_order_priority = 0
metadata/_custom_type_script = "uid://c7s4elarc1emd"
