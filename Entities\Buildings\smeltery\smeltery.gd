class_name Smelter
extends ConveyorBasedBuilding

var is_set: bool = false
var recipe: ItemRecipe
var new_item_data: ItemData


@onready var power_receiver_component: PowerReceiverComponent = $Components/PowerReceiverComponent
@onready var animated_sprite_2d: AnimatedSprite2D = $AnimatedSprite2D


func _ready() -> void:
	power_receiver_component.power_received.connect(_power_received)


func _process(_delta: float) -> void:
	if Engine.is_editor_hint():
		return

	# Wait for item
	if _held_item == null:
		return
		
	# Wait for animation to finish (item to arrive)
	if _held_item.is_moving():
		return
	
	if not is_set:
		is_set = true
		
		# Get recipe if there is any for this item
		recipe = UnlockManager.get_simple_recipe(stats.building_type, _held_item.item_data)
		if recipe:
			new_item_data = recipe.output_resources.keys().front()

	# Heat up item to target temp
	var target_temp = _held_item.item_data.temperature
	if new_item_data:
		target_temp = new_item_data.temperature

	if _held_item.temperature < target_temp:
		power_receiver_component.efficiency = 1
		if not animated_sprite_2d.is_playing():
			animated_sprite_2d.play(animated_sprite_2d.animation)
		return
	
	power_receiver_component.efficiency = 0

	# Wait for output to be available
	if output_buildings.is_empty():
		return
	
	# Perform operation on item
	if recipe:
		_held_item.item_data = new_item_data
		_held_item.reload_stats()
	
	# Release item
	reset()
	push_item.emit(_held_item)


func reset():
	is_set = false
	recipe = null
	new_item_data = null


func _power_received(power: Variant, delta: Variant) -> void:
	# Does not have item but recieved some power
	assert(_held_item or power == 0)
	
	if not _held_item:
		return
	
	var provided_heating = 1000 * (power / power_receiver_component.power_amount) * delta
	_held_item.temperature += provided_heating
