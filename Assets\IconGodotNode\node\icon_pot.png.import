[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://ocnp0bta8yiu"
path="res://.godot/imported/icon_pot.png-da8a52d3bf72bd1d6d9694d2d78e1b96.ctex"
metadata={
"vram_texture": false
}

[deps]

source_file="res://Assets/IconGodotNode/node/icon_pot.png"
dest_files=["res://.godot/imported/icon_pot.png-da8a52d3bf72bd1d6d9694d2d78e1b96.ctex"]

[params]

compress/mode=0
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=false
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=1
