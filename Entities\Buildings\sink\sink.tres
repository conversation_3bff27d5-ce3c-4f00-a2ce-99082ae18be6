[gd_resource type="Resource" script_class="ItemHandlingBuildingStats" load_steps=5 format=3 uid="uid://ntltx0x1d7hr"]

[ext_resource type="Script" uid="uid://qgrxre6nc1iy" path="res://Entities/Buildings/sink/sink.gd" id="1_yarxf"]
[ext_resource type="Script" uid="uid://qrdti4s4su0t" path="res://Entities/Buildings/building base/conveyor_based_building_stats.gd" id="2_4mmnx"]
[ext_resource type="Texture2D" uid="uid://m6m10372pvs7" path="res://Assets/Sprites/32x32/SpriteSheets/Sink_sprite_sheet_anim.png" id="3_q45b2"]

[sub_resource type="AtlasTexture" id="AtlasTexture_jigge"]
atlas = ExtResource("3_q45b2")
region = Rect2(0, 0, 32, 32)

[resource]
resource_name = "Sink"
script = ExtResource("2_4mmnx")
input_directions = 15
output_directions = 0
transport_speed = 0.0
translation_key = "SINK"
dimensions = Vector2i(1, 1)
is_rotateable = false
building_type = 7
component_scripts = Array[Script]([])
cost = Dictionary[int, int]({})
texture = SubResource("AtlasTexture_jigge")
building_script = ExtResource("1_yarxf")
unlocked_by_default = false
menu_order_priority = 0
metadata/_custom_type_script = "uid://qrdti4s4su0t"
