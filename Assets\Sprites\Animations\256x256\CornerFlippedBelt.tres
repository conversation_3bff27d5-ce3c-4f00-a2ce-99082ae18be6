[gd_resource type="SpriteFrames" load_steps=38 format=3 uid="uid://df7kwxx3q51yu"]

[ext_resource type="Texture2D" uid="uid://wlaplenty42h" path="res://Assets/Sprites/SpriteSheets/CornerNewFlippedSmall_sprite_sheet_anim.png" id="1_lhg5r"]

[sub_resource type="AtlasTexture" id="AtlasTexture_6q4s8"]
atlas = ExtResource("1_lhg5r")
region = Rect2(0, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_y5a0d"]
atlas = ExtResource("1_lhg5r")
region = Rect2(256, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_or4le"]
atlas = ExtResource("1_lhg5r")
region = Rect2(512, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_oranq"]
atlas = ExtResource("1_lhg5r")
region = Rect2(768, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_itgf0"]
atlas = ExtResource("1_lhg5r")
region = Rect2(1024, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_jhcmp"]
atlas = ExtResource("1_lhg5r")
region = Rect2(1280, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_apuym"]
atlas = ExtResource("1_lhg5r")
region = Rect2(1536, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_31bcf"]
atlas = ExtResource("1_lhg5r")
region = Rect2(1792, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_culf6"]
atlas = ExtResource("1_lhg5r")
region = Rect2(2048, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_a2d1j"]
atlas = ExtResource("1_lhg5r")
region = Rect2(0, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_vehoy"]
atlas = ExtResource("1_lhg5r")
region = Rect2(256, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_5ecvl"]
atlas = ExtResource("1_lhg5r")
region = Rect2(512, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_skjxc"]
atlas = ExtResource("1_lhg5r")
region = Rect2(768, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_3lin8"]
atlas = ExtResource("1_lhg5r")
region = Rect2(1024, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_3tdxm"]
atlas = ExtResource("1_lhg5r")
region = Rect2(1280, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_b4n76"]
atlas = ExtResource("1_lhg5r")
region = Rect2(1536, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_r7f0i"]
atlas = ExtResource("1_lhg5r")
region = Rect2(1792, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_u2fsj"]
atlas = ExtResource("1_lhg5r")
region = Rect2(2048, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_jsee2"]
atlas = ExtResource("1_lhg5r")
region = Rect2(0, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_3vev1"]
atlas = ExtResource("1_lhg5r")
region = Rect2(256, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_33ek4"]
atlas = ExtResource("1_lhg5r")
region = Rect2(512, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_b60q0"]
atlas = ExtResource("1_lhg5r")
region = Rect2(768, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_ihqs7"]
atlas = ExtResource("1_lhg5r")
region = Rect2(1024, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_x8fx5"]
atlas = ExtResource("1_lhg5r")
region = Rect2(1280, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_s3url"]
atlas = ExtResource("1_lhg5r")
region = Rect2(1536, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_8acun"]
atlas = ExtResource("1_lhg5r")
region = Rect2(1792, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_brsqm"]
atlas = ExtResource("1_lhg5r")
region = Rect2(2048, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_15myd"]
atlas = ExtResource("1_lhg5r")
region = Rect2(0, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_r8i6s"]
atlas = ExtResource("1_lhg5r")
region = Rect2(256, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_5iofn"]
atlas = ExtResource("1_lhg5r")
region = Rect2(512, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_v2yfq"]
atlas = ExtResource("1_lhg5r")
region = Rect2(768, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_m663i"]
atlas = ExtResource("1_lhg5r")
region = Rect2(1024, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_gjsbd"]
atlas = ExtResource("1_lhg5r")
region = Rect2(1280, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_bi1wb"]
atlas = ExtResource("1_lhg5r")
region = Rect2(1536, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_ojchx"]
atlas = ExtResource("1_lhg5r")
region = Rect2(1792, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_o5b0k"]
atlas = ExtResource("1_lhg5r")
region = Rect2(2048, 256, 256, 256)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_6q4s8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_y5a0d")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_or4le")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_oranq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_itgf0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_jhcmp")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_apuym")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_31bcf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_culf6")
}],
"loop": true,
"name": &"Down",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_a2d1j")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vehoy")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5ecvl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_skjxc")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3lin8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3tdxm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_b4n76")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_r7f0i")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_u2fsj")
}],
"loop": true,
"name": &"Left",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_jsee2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3vev1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_33ek4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_b60q0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ihqs7")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_x8fx5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_s3url")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8acun")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_brsqm")
}],
"loop": true,
"name": &"Right",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_15myd")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_r8i6s")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5iofn")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_v2yfq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_m663i")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gjsbd")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bi1wb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ojchx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_o5b0k")
}],
"loop": true,
"name": &"Up",
"speed": 10.0
}]
