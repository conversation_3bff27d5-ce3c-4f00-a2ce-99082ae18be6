class_name NPC
extends CharacterBody2D

@export var navigation: NavigationAgent2D
@export var move_speed: float = 200

# Movement order queue system
var movement_orders: Array[Vector2] = []


func _ready() -> void:
	navigation.velocity_computed.connect(set_velocity)



func move():
	var next_path: Vector2      = navigation.get_next_path_position()
	var move_direction: Vector2 = global_position.direction_to(next_path)
	var new_velocity: Vector2   = move_direction * move_speed

	if navigation.avoidance_enabled:
		navigation.velocity = new_velocity
	else:
		velocity = new_velocity

	move_and_slide()



# Queue management functions
func add_movement_order(target_pos: Vector2) -> void:
	movement_orders.append(target_pos)



func get_next_movement_order() -> Vector2:
	if movement_orders.size() > 0:
		return movement_orders.pop_front()
	return Vector2.ZERO



func has_movement_orders() -> bool:
	return movement_orders.size() > 0



func clear_movement_orders() -> void:
	movement_orders.clear()
