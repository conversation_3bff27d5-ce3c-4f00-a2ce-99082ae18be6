class_name PowerPlant
extends ConveyorBasedBuilding

var max_power: float = 100


@onready var power_provider_component: PowerProviderComponent = $Components/PowerProviderComponent
@onready var power_buffer_component: PowerBufferComponent = $Components/PowerBufferComponent
@onready var animated_sprite_2d: AnimatedSprite2D = $AnimatedSprite2D


func _ready() -> void:
	power_provider_component.power_provided.connect(_power_drawn)
	power_provider_component.power_amount = max_power/2
	
	power_buffer_component.max_storage = max_power


func _on_push_item(item: Item):
	if item == null:
		return

	if _held_item != null:
		return
		
	if item.item_data.type != ItemType.Enum.COAL:
		return
	
	item_received.emit(item, self)


func _process(_delta: float) -> void:
	if Engine.is_editor_hint():
		return

	# Wait for item
	if _held_item == null:
		return
		
	# Wait for animation to finish (item to arrive)
	if _held_item.is_moving():
		return
	
	var power_generated = max_power / 3
	if power_buffer_component.stored_power < max_power + power_generated:
		power_buffer_component.stored_power += power_generated
		_held_item.queue_free()


func _power_drawn(power: Variant, delta: Variant) -> void:
	power_buffer_component.stored_power -= power * delta
	
	var power_avaible = power_buffer_component.stored_power
	var power_stored = power_buffer_component.max_storage
	var ratio = power_avaible/power_stored
	
	if ratio < 0.3:
		animated_sprite_2d.set_frame_and_progress(0,0)
	elif ratio < 0.6:
		animated_sprite_2d.set_frame_and_progress(1,0)
	else:
		animated_sprite_2d.set_frame_and_progress(2,0)
