[gd_resource type="SpriteFrames" load_steps=38 format=3 uid="uid://dnhnidhk8fku3"]

[ext_resource type="Texture2D" uid="uid://cnmoc2hn7bnki" path="res://Assets/Sprites/SpriteSheets/ThreeSideMergeNewSmall_sprite_sheet_anim.png" id="1_3jqrv"]

[sub_resource type="AtlasTexture" id="AtlasTexture_fi8y4"]
atlas = ExtResource("1_3jqrv")
region = Rect2(0, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_6e4lt"]
atlas = ExtResource("1_3jqrv")
region = Rect2(256, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_vy3la"]
atlas = ExtResource("1_3jqrv")
region = Rect2(512, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_klir0"]
atlas = ExtResource("1_3jqrv")
region = Rect2(768, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_w8ccm"]
atlas = ExtResource("1_3jqrv")
region = Rect2(1024, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_1dgxf"]
atlas = ExtResource("1_3jqrv")
region = Rect2(1280, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_ch6vl"]
atlas = ExtResource("1_3jqrv")
region = Rect2(1536, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_bnvc4"]
atlas = ExtResource("1_3jqrv")
region = Rect2(1792, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_2p46a"]
atlas = ExtResource("1_3jqrv")
region = Rect2(2048, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_vlv7y"]
atlas = ExtResource("1_3jqrv")
region = Rect2(0, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_0q0nu"]
atlas = ExtResource("1_3jqrv")
region = Rect2(256, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_invj8"]
atlas = ExtResource("1_3jqrv")
region = Rect2(512, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_8fiwd"]
atlas = ExtResource("1_3jqrv")
region = Rect2(768, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_u1ph0"]
atlas = ExtResource("1_3jqrv")
region = Rect2(1024, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_f35rq"]
atlas = ExtResource("1_3jqrv")
region = Rect2(1280, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_x8w7g"]
atlas = ExtResource("1_3jqrv")
region = Rect2(1536, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_dqs4d"]
atlas = ExtResource("1_3jqrv")
region = Rect2(1792, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_8urh0"]
atlas = ExtResource("1_3jqrv")
region = Rect2(2048, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_yg23d"]
atlas = ExtResource("1_3jqrv")
region = Rect2(0, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_ug4qi"]
atlas = ExtResource("1_3jqrv")
region = Rect2(256, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_mvykx"]
atlas = ExtResource("1_3jqrv")
region = Rect2(512, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_q36vb"]
atlas = ExtResource("1_3jqrv")
region = Rect2(768, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_fymg7"]
atlas = ExtResource("1_3jqrv")
region = Rect2(1024, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_7p6na"]
atlas = ExtResource("1_3jqrv")
region = Rect2(1280, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_tn8ts"]
atlas = ExtResource("1_3jqrv")
region = Rect2(1536, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_gqhpx"]
atlas = ExtResource("1_3jqrv")
region = Rect2(1792, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_r1f3j"]
atlas = ExtResource("1_3jqrv")
region = Rect2(2048, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_y7e4t"]
atlas = ExtResource("1_3jqrv")
region = Rect2(0, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_sppsa"]
atlas = ExtResource("1_3jqrv")
region = Rect2(256, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_7j27t"]
atlas = ExtResource("1_3jqrv")
region = Rect2(512, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_di0n5"]
atlas = ExtResource("1_3jqrv")
region = Rect2(768, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_wjdht"]
atlas = ExtResource("1_3jqrv")
region = Rect2(1024, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_b7fx3"]
atlas = ExtResource("1_3jqrv")
region = Rect2(1280, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_w8ggd"]
atlas = ExtResource("1_3jqrv")
region = Rect2(1536, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_y1o1o"]
atlas = ExtResource("1_3jqrv")
region = Rect2(1792, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_8vljm"]
atlas = ExtResource("1_3jqrv")
region = Rect2(2048, 256, 256, 256)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_fi8y4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6e4lt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vy3la")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_klir0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_w8ccm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_1dgxf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ch6vl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bnvc4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_2p46a")
}],
"loop": true,
"name": &"Down",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_vlv7y")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_0q0nu")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_invj8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8fiwd")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_u1ph0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_f35rq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_x8w7g")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_dqs4d")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8urh0")
}],
"loop": true,
"name": &"Left",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_yg23d")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ug4qi")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mvykx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_q36vb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_fymg7")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7p6na")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tn8ts")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gqhpx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_r1f3j")
}],
"loop": true,
"name": &"Right",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_y7e4t")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_sppsa")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7j27t")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_di0n5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wjdht")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_b7fx3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_w8ggd")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_y1o1o")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8vljm")
}],
"loop": true,
"name": &"Up",
"speed": 10.0
}]
