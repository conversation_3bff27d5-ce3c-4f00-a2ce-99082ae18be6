extends Node2D

signal demolition_mode_cancelled

@onready var tilemap : TileMapLayer = $"../Tiles/Ground"


func _cancel_demolition():
	StateManager.state = StateManager.States.STATE_PLAY
	demolition_mode_cancelled.emit()


func _remove_building_at_position(tilemap_position : Vector2):
	if !BuildingModeManager.is_building_site_occupied(tilemap_position):
		return
	var building : Node2D = get_building_at_position(tilemap_position)
	set_building_sites_as_free(building)
	BuildingSignalBus.building_demolished.emit(building)
	building.queue_free()
	AudioManager.create_audio(SoundEffect.SOUND_EFFECT_TYPE.BUILDING_DESTROYED)


func get_building_at_position(tilemap_position : Vector2i) -> Node2D:
	var building :Node2D = BuildingModeManager.occupied_tiles.get(tilemap_position)
	return building


func set_building_sites_as_free(building : Node2D) -> void:
	var tile : Vector2i
	while BuildingModeManager.occupied_tiles.find_key(building) != null:
		tile = BuildingModeManager.occupied_tiles.find_key(building)
		BuildingModeManager.occupied_tiles.erase(tile)

var is_dragging : bool = false
var current_mouse_tile : Vector2i = Vector2i.ZERO
var new_mouse_tile : Vector2i = Vector2i.ZERO

func _on_hud_caught_input(event : InputEvent) -> void:
	if StateManager.state != StateManager.States.STATE_DEMOLISH:
			return
	if event is InputEventMouseButton:
		if event.button_index == MOUSE_BUTTON_LEFT:
			var local_mouse_pos : Vector2 = to_local(get_global_mouse_position())
			var tilemap_building_position : Vector2i = tilemap.local_to_map(local_mouse_pos)
			
			if event.is_pressed():
				current_mouse_tile = tilemap.local_to_map(local_mouse_pos)
				_remove_building_at_position(tilemap_building_position)
				is_dragging = true
				
			if event.is_released():
				is_dragging = false
				
		if event.button_index == MOUSE_BUTTON_RIGHT:
			_cancel_demolition()
	
func _process(_delta):
	if is_dragging:
		var local_mouse_pos = to_local(get_global_mouse_position())
		new_mouse_tile = tilemap.local_to_map(local_mouse_pos)
		if new_mouse_tile != current_mouse_tile:
			current_mouse_tile = new_mouse_tile
			_remove_building_at_position(current_mouse_tile)
	



	
