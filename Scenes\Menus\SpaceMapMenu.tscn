[gd_scene load_steps=10 format=3 uid="uid://ck5jwl6m0hosg"]

[ext_resource type="Script" uid="uid://btc6te8igj7f8" path="res://Scenes/Menus/space_map_menu.gd" id="1_d61je"]
[ext_resource type="Script" uid="uid://d2s7r1nqci5th" path="res://Entities/NodeMap/space_camera.gd" id="1_xws8f"]
[ext_resource type="Script" uid="uid://bad08elc438by" path="res://Scenes/camera_limiter.gd" id="2_5e1ev"]
[ext_resource type="PackedScene" uid="uid://bm2niqbcccmqu" path="res://Entities/NodeMap/SpaceMap/planet_node.tscn" id="2_8u05b"]
[ext_resource type="Texture2D" uid="uid://cnc8wrrgojiw8" path="res://Assets/Sprites/TempBack.png" id="3_a6bim"]
[ext_resource type="PackedScene" uid="uid://0ihbkwcfs6yp" path="res://Scenes/Playable/PlanetSurface.tscn" id="3_xws8f"]
[ext_resource type="Texture2D" uid="uid://byxx30nvmbm05" path="res://Assets/Sprites/TempBackHoverClicked.png" id="4_u03hk"]
[ext_resource type="Material" uid="uid://bc3n5r7ecf8f1" path="res://Shaders/StarBacgroundMaterial.tres" id="5_4ia1w"]
[ext_resource type="LabelSettings" uid="uid://dybh11bdm6mcy" path="res://Assets/Text/Header.tres" id="6_a6bim"]

[node name="SpaceMapMenu" type="Node2D"]
script = ExtResource("1_d61je")

[node name="CanvasLayer" type="CanvasLayer" parent="."]
layer = -1

[node name="ColorRect" type="ColorRect" parent="CanvasLayer"]
material = ExtResource("5_4ia1w")
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="BackButton" type="TextureButton" parent="CanvasLayer"]
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -120.0
offset_top = 45.0
offset_right = -56.0
offset_bottom = 109.0
grow_horizontal = 0
texture_normal = ExtResource("3_a6bim")
texture_pressed = ExtResource("4_u03hk")
texture_hover = ExtResource("4_u03hk")
stretch_mode = 4

[node name="Label" type="Label" parent="CanvasLayer"]
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -119.0
offset_top = 13.0
offset_right = 177.0
offset_bottom = 58.0
grow_horizontal = 2
text = "PLANET SELECTION"
label_settings = ExtResource("6_a6bim")

[node name="ExitToMenuButton" type="Button" parent="CanvasLayer"]
offset_left = 28.0
offset_top = 37.0
offset_right = 219.0
offset_bottom = 87.0
theme_override_font_sizes/font_size = 30
text = "Exit to menu"

[node name="Camera2D" type="Camera2D" parent="." node_paths=PackedStringArray("border")]
script = ExtResource("1_xws8f")
border = NodePath("../CameraLimiter")

[node name="CameraLimiter" type="ReferenceRect" parent="."]
offset_left = -1800.0
offset_top = 201.0
offset_right = -147.0
offset_bottom = 1078.0
mouse_filter = 2
border_width = 3.0
editor_only = false
script = ExtResource("2_5e1ev")

[node name="Planet1" parent="CameraLimiter" node_paths=PackedStringArray("linked_nodes") instance=ExtResource("2_8u05b")]
position = Vector2(100, 85)
planet_scene = ExtResource("3_xws8f")
linked_nodes = [NodePath("../Planet2"), NodePath("../Planet3")]
node_state = 2

[node name="Planet2" parent="CameraLimiter" instance=ExtResource("2_8u05b")]
position = Vector2(376, 555)

[node name="Planet3" parent="CameraLimiter" node_paths=PackedStringArray("linked_nodes") instance=ExtResource("2_8u05b")]
position = Vector2(780, 222)
linked_nodes = [NodePath("../Planet4")]

[node name="Planet4" parent="CameraLimiter" node_paths=PackedStringArray("linked_nodes") instance=ExtResource("2_8u05b")]
position = Vector2(1244, 287)
linked_nodes = [NodePath("../Planet5")]

[node name="Planet5" parent="CameraLimiter" instance=ExtResource("2_8u05b")]
position = Vector2(1539, 774)

[connection signal="pressed" from="CanvasLayer/BackButton" to="." method="_on_back_button_pressed"]
[connection signal="pressed" from="CanvasLayer/ExitToMenuButton" to="." method="_on_exit_to_menu_button_pressed"]
