class_name NodeConnection
extends Node2D

@export var connection_duration  := 0.5
@export var expansion_duration   := 0.1
@export var contraction_duration := 0.2

@export var primary_line: Line2D
@export var secondary_line: Line2D

var pos_a: Vector2
var pos_b: Vector2

var _is_active := false

var is_active: bool:
	get():
		return _is_active

signal connection_activated

func initialize(start_node: UnlockableNode, end_node: UnlockableNode):
	pos_a = start_node.global_position - self.global_position
	pos_b = end_node.global_position - self.global_position
	
	primary_line.add_point(pos_a)
	primary_line.add_point(pos_b)

	secondary_line.add_point(pos_a)
	secondary_line.add_point(pos_a)
	
	start_node.outgoing_links.append(self)
	end_node.incoming_links.append(self)
	
	# This just load previous state from save
	var data := SaveManager.get_scene_data(self)
	var active = data.get_or_add("is_active", _is_active)
	connection_activated.connect(
		func():
			data.set("is_active", true)
	)
	
	# Skips animation if it was already active
	if active:
		set_active()


## Plays fancy animation and activates node
func activate():
	if _is_active:
		return
	
	var tween := get_tree().create_tween()

	# Animate line length
	tween.tween_method(
		func(p): secondary_line.set_point_position(1, p),
			pos_a, pos_b, connection_duration
	).set_trans(Tween.TRANS_CUBIC)

	# Animate width expansion (after line length animation finishes)
	var normal_width   := secondary_line.width
	var expanded_width := normal_width * 4
	tween.tween_method(
		func(p): secondary_line.width = p,
			normal_width, expanded_width, expansion_duration
	).set_trans(Tween.TRANS_BOUNCE)

	# Animate width contraction (after expansion)
	tween.tween_method(
		func(p): secondary_line.width = p,
			expanded_width, normal_width, connection_duration
	).set_trans(Tween.TRANS_CUBIC)

	set_active()


## Just activates connection (No fancy animation)
func set_active():
	if _is_active:
		return
	
	_is_active = true
	secondary_line.set_point_position(1, pos_b)
	connection_activated.emit()
