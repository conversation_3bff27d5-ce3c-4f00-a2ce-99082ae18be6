[gd_scene load_steps=8 format=3 uid="uid://bfswc82awrvmx"]

[ext_resource type="StyleBox" uid="uid://8en3eownniur" path="res://Assets/Styles/GenericButtonHovered.tres" id="1_fojlm"]
[ext_resource type="Script" uid="uid://cd8ah0umd5lc4" path="res://Scenes/Menus/SaveSelect/save_slot.gd" id="1_kurao"]
[ext_resource type="StyleBox" uid="uid://br1jxnl5ox4pr" path="res://Assets/Styles/GenericButtonClicked.tres" id="2_0wix8"]
[ext_resource type="StyleBox" uid="uid://byicfcubdmq4h" path="res://Assets/Styles/GenericButton.tres" id="3_hnmed"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ge43p"]
bg_color = Color(0.6, 0.6, 0.6, 0)
border_width_left = 4
border_width_top = 4
border_width_right = 4
border_width_bottom = 4

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_d7w4s"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_lic4x"]

[node name="SaveSlot" type="Panel"]
custom_minimum_size = Vector2(500, 200)
size_flags_horizontal = 4
size_flags_vertical = 4
theme_override_styles/panel = SubResource("StyleBoxFlat_ge43p")
script = ExtResource("1_kurao")

[node name="NewGameOption" type="VBoxContainer" parent="."]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
alignment = 1

[node name="NewGameButton" type="Button" parent="NewGameOption"]
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 4
theme_override_colors/font_hover_pressed_color = Color(1, 1, 1, 1)
theme_override_colors/font_hover_color = Color(1, 1, 1, 1)
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_colors/font_focus_color = Color(1, 1, 1, 1)
theme_override_colors/font_pressed_color = Color(1, 1, 1, 1)
theme_override_font_sizes/font_size = 30
theme_override_styles/focus = SubResource("StyleBoxEmpty_d7w4s")
theme_override_styles/hover = ExtResource("1_fojlm")
theme_override_styles/pressed = ExtResource("2_0wix8")
theme_override_styles/normal = ExtResource("3_hnmed")
text = "New game"

[node name="ContinueGameOption" type="VBoxContainer" parent="."]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
alignment = 1

[node name="HBoxContainer" type="HBoxContainer" parent="ContinueGameOption"]
layout_mode = 2
alignment = 1

[node name="TotalStarLabel" type="Label" parent="ContinueGameOption/HBoxContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(0.778942, 0.687775, 0.241153, 1)
theme_override_font_sizes/font_size = 20
text = "Total stars:"
horizontal_alignment = 1

[node name="TotalStarNumber" type="Label" parent="ContinueGameOption"]
layout_mode = 2
theme_override_font_sizes/font_size = 20
text = "42"
horizontal_alignment = 1

[node name="PlaytimeLabel" type="Label" parent="ContinueGameOption"]
layout_mode = 2
theme_override_colors/font_color = Color(0.403538, 0.732483, 0.306802, 1)
theme_override_font_sizes/font_size = 20
text = "Playtime:"
horizontal_alignment = 1

[node name="PlaytimeNumber" type="Label" parent="ContinueGameOption"]
layout_mode = 2
theme_override_font_sizes/font_size = 20
text = "69h 42m 33s"
horizontal_alignment = 1

[node name="ContinueButton" type="Button" parent="ContinueGameOption"]
layout_mode = 2
size_flags_horizontal = 4
theme_override_font_sizes/font_size = 30
theme_override_styles/focus = SubResource("StyleBoxEmpty_lic4x")
theme_override_styles/hover = ExtResource("1_fojlm")
theme_override_styles/pressed = ExtResource("2_0wix8")
theme_override_styles/normal = ExtResource("3_hnmed")
text = "Continue"

[connection signal="pressed" from="NewGameOption/NewGameButton" to="." method="_on_new_game_button_pressed"]
[connection signal="pressed" from="ContinueGameOption/ContinueButton" to="." method="_on_continue_button_pressed"]
