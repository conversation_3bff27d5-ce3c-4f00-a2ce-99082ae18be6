@tool
extends BTAction

var npc: DetectionComponent

@export var entities_var := &"entities"


func _generate_name() -> String:
	return "Near entities to %s" % entities_var


func _setup() -> void:
	npc = agent.get_node_or_null("DetectionComponent")
	if npc == null:
		push_error("DetectionComponent node not found as a child of the parent!")


func _tick(_delta: float) -> Status:
	var entities := npc.get_near_enemies()
	if entities.size() == 0:
		return FAILURE
	blackboard.set_var(entities_var, entities)
	return SUCCESS
