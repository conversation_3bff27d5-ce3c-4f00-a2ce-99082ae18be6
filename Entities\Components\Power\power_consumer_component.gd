class_name PowerReceiverComponent
extends PowerBaseComponent

## Power recieved by component from power network
@warning_ignore("unused_signal")
signal power_received(power, delta)

## How much of current input can be used right now
var efficiency: float = 0.0

## How much power this input can transfer
@export var power_amount: float = 10.0
## Power priority group of this input
@export var power_priority := PowerInputPrioriy.Enum.GENERAL

func _ready() -> void:
	add_to_group(&"power_receiver", true)

func _enter_tree() -> void:
	PlanetSignalBus.power_reciever_added.emit(self)

func _exit_tree() -> void:
	PlanetSignalBus.power_reciever_removed.emit(self)
