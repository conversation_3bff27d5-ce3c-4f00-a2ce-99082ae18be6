[gd_scene load_steps=27 format=3 uid="uid://ciss42d8yriru"]

[ext_resource type="SpriteFrames" uid="uid://qx7qkbtalpo0" path="res://Assets/Sprites/Animations/LowPolyPlanetAnimation.tres" id="1_os3g4"]
[ext_resource type="SpriteFrames" uid="uid://deq1e81m0rmy" path="res://Assets/Sprites/Animations/HighPolyPlanetAnimation.tres" id="2_03uxg"]
[ext_resource type="Texture2D" uid="uid://ccs3p4paaw1yd" path="res://Assets/Sprites/HighDetailBelt_sprite_sheet_anim.png" id="3_10tfp"]
[ext_resource type="Texture2D" uid="uid://b0weej2p3bdvu" path="res://Assets/Sprites/LowdetailBelt_sprite_sheet_anim.png" id="4_owm11"]
[ext_resource type="SpriteFrames" uid="uid://8y7oliaxg0wc" path="res://Assets/Sprites/Animations/256x256/FurnaceBelt.tres" id="5_epv6m"]
[ext_resource type="SpriteFrames" uid="uid://btjmd6fwiovoc" path="res://Assets/Sprites/Animations/64x64/Powerplant.tres" id="6_epv6m"]

[sub_resource type="AtlasTexture" id="AtlasTexture_epv6m"]
atlas = ExtResource("3_10tfp")
region = Rect2(0, 0, 255, 255)

[sub_resource type="AtlasTexture" id="AtlasTexture_tpe8n"]
atlas = ExtResource("3_10tfp")
region = Rect2(255, 0, 255, 255)

[sub_resource type="AtlasTexture" id="AtlasTexture_s7lnj"]
atlas = ExtResource("3_10tfp")
region = Rect2(510, 0, 255, 255)

[sub_resource type="AtlasTexture" id="AtlasTexture_tkcjs"]
atlas = ExtResource("3_10tfp")
region = Rect2(765, 0, 255, 255)

[sub_resource type="AtlasTexture" id="AtlasTexture_3j4pf"]
atlas = ExtResource("3_10tfp")
region = Rect2(1020, 0, 255, 255)

[sub_resource type="AtlasTexture" id="AtlasTexture_33rsh"]
atlas = ExtResource("3_10tfp")
region = Rect2(1275, 0, 255, 255)

[sub_resource type="AtlasTexture" id="AtlasTexture_1ku1l"]
atlas = ExtResource("3_10tfp")
region = Rect2(1530, 0, 255, 255)

[sub_resource type="AtlasTexture" id="AtlasTexture_5k8a1"]
atlas = ExtResource("3_10tfp")
region = Rect2(1785, 0, 255, 255)

[sub_resource type="AtlasTexture" id="AtlasTexture_gnckx"]
atlas = ExtResource("3_10tfp")
region = Rect2(2040, 0, 255, 255)

[sub_resource type="SpriteFrames" id="SpriteFrames_qisxm"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_epv6m")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tpe8n")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_s7lnj")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tkcjs")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3j4pf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_33rsh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_1ku1l")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5k8a1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gnckx")
}],
"loop": true,
"name": &"default",
"speed": 15.0
}]

[sub_resource type="AtlasTexture" id="AtlasTexture_377l3"]
atlas = ExtResource("4_owm11")
region = Rect2(0, 0, 255, 255)

[sub_resource type="AtlasTexture" id="AtlasTexture_twwaw"]
atlas = ExtResource("4_owm11")
region = Rect2(255, 0, 255, 255)

[sub_resource type="AtlasTexture" id="AtlasTexture_eltlr"]
atlas = ExtResource("4_owm11")
region = Rect2(510, 0, 255, 255)

[sub_resource type="AtlasTexture" id="AtlasTexture_6k53v"]
atlas = ExtResource("4_owm11")
region = Rect2(765, 0, 255, 255)

[sub_resource type="AtlasTexture" id="AtlasTexture_ohkl5"]
atlas = ExtResource("4_owm11")
region = Rect2(1020, 0, 255, 255)

[sub_resource type="AtlasTexture" id="AtlasTexture_6in8d"]
atlas = ExtResource("4_owm11")
region = Rect2(1275, 0, 255, 255)

[sub_resource type="AtlasTexture" id="AtlasTexture_n5c7d"]
atlas = ExtResource("4_owm11")
region = Rect2(1530, 0, 255, 255)

[sub_resource type="AtlasTexture" id="AtlasTexture_3nnhh"]
atlas = ExtResource("4_owm11")
region = Rect2(1785, 0, 255, 255)

[sub_resource type="AtlasTexture" id="AtlasTexture_2qmg3"]
atlas = ExtResource("4_owm11")
region = Rect2(2040, 0, 255, 255)

[sub_resource type="SpriteFrames" id="SpriteFrames_8s0km"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_377l3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_twwaw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_eltlr")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6k53v")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ohkl5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6in8d")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_n5c7d")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3nnhh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_2qmg3")
}],
"loop": true,
"name": &"default",
"speed": 15.0
}]

[node name="AnimatingPlanets" type="Node2D"]

[node name="AnimatedSprite2D" type="AnimatedSprite2D" parent="."]
position = Vector2(229, -3)
sprite_frames = ExtResource("1_os3g4")
frame_progress = 0.348438

[node name="AnimatedSprite2D2" type="AnimatedSprite2D" parent="."]
sprite_frames = ExtResource("2_03uxg")
frame_progress = 0.0188207

[node name="AnimatedSprite2D3" type="AnimatedSprite2D" parent="."]
position = Vector2(483.5, 129.5)
sprite_frames = SubResource("SpriteFrames_qisxm")
frame_progress = 0.178484

[node name="AnimatedSprite2D4" type="AnimatedSprite2D" parent="."]
position = Vector2(483.5, 314.5)
sprite_frames = SubResource("SpriteFrames_8s0km")
frame = 4
frame_progress = 0.710768

[node name="AnimatedSprite2D5" type="AnimatedSprite2D" parent="."]
position = Vector2(167, 403)
sprite_frames = ExtResource("5_epv6m")
animation = &"Down"
frame_progress = 0.0642629

[node name="AnimatedSprite2D6" type="AnimatedSprite2D" parent="."]
position = Vector2(890, 441)
sprite_frames = ExtResource("6_epv6m")
animation = &"West"
