name: "godot-release"
on:
  push:
    tags:
      - 'v*'   # Trigger release on version tags, e.g. v1.0.0

env:
  GODOT_VERSION: 4.4.1
  EXPORT_NAME: ReFactory
  PROJECT_PATH: .

jobs:
  export-windows:
    name: Windows Build
    runs-on: ubuntu-22.04
    container:
      image: barichello/godot-ci:4.4.1
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          lfs: true
      - name: Setup
        run: |
          mkdir -v -p ~/.local/share/godot/export_templates/
          mkdir -v -p ~/.config/
          mv /root/.config/godot ~/.config/godot
          mv /root/.local/share/godot/export_templates/${GODOT_VERSION}.stable ~/.local/share/godot/export_templates/${GODOT_VERSION}.stable
      - name: Windows Build
        run: |
          mkdir -v -p build/windows
          EXPORT_DIR="$(readlink -f build)"
          cd $PROJECT_PATH
          godot --headless --verbose --export-release "windows" "$EXPORT_DIR/windows/$EXPORT_NAME.exe"
      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: windows
          path: build/windows
      - name: Create GitHub Release and Upload Artifact
        if: startsWith(github.ref, 'refs/tags/v')
        uses: ncipollo/release-action@v1.18.0
        with:
          tag: ${{ github.ref_name }}
          name: Release ${{ github.ref_name }}
          draft: false
          artifacts: build/windows