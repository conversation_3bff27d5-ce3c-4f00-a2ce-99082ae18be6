@tool
class_name DetectionComponent
extends Node2D

var near_enemies := {}
var far_enemies  := {}
var _last_cleanup_frame := -1

@export var debug: bool = false
@export var nearArea: Area2D
@export var farArea: Area2D

func _ready() -> void:
	if Engine.is_editor_hint():
		_editor_setup()
		return

	# Connect engageRadius signals if set
	if nearArea:
		nearArea.body_entered.connect(_on_engage_radius_body_entered)
		nearArea.body_exited.connect(_on_engage_radius_body_exited)
	else:
		push_warning("engageRadius not set on DetectionComponent")

	# Connect detectionRadius signals if set
	if farArea:
		farArea.body_entered.connect(_on_detection_radius_body_entered)
		farArea.body_exited.connect(_on_detection_radius_body_exited)
	else:
		push_warning("detectionRadius not set on DetectionComponent")


func _editor_setup() -> void:
	if not nearArea:
		nearArea = get_node("Inner")

	if not farArea:
		farArea = get_node("Outer")

	if nearArea:
		nearArea.set_collision_layer_value(1, false)
		nearArea.set_collision_layer_value(2, false)
		nearArea.set_collision_mask_value(1, true)
		nearArea.set_collision_mask_value(2, true)

		var collision_shape := nearArea.get_node("CollisionShape2D")
		if collision_shape:
			collision_shape.set_debug_color(Color(1.0, 0.5, 0.0, 0.1))

	if farArea:
		farArea.set_collision_layer_value(1, false)
		farArea.set_collision_layer_value(2, false)
		farArea.set_collision_mask_value(1, true)
		farArea.set_collision_mask_value(2, true)

		var collision_shape := farArea.get_node("CollisionShape2D")
		if collision_shape:
			collision_shape.set_debug_color(Color(0.2, 1.0, 0.0, 0.1))

func _on_engage_radius_body_entered(body: Node2D) -> void:
	# Prevent self detection
	if body == self.get_parent():
		return

	var entity_id: int = body.get_instance_id()
	if not near_enemies.has(entity_id):
		if debug: print(body.name, " is engagable")
		near_enemies[entity_id] = body



func _on_engage_radius_body_exited(body: Node2D) -> void:
	var entity_id: int = body.get_instance_id()
	if near_enemies.has(entity_id):
		if debug: print(body.name, " is not engagable")
		near_enemies.erase(entity_id)



func _on_detection_radius_body_entered(body: Node2D) -> void:
	# Prevent self detection
	if body == self.get_parent():
		return

	var entity_id: int = body.get_instance_id()
	if not far_enemies.has(entity_id):
		if debug: print(body.name, " is detectable")
		far_enemies[entity_id] = body



func _on_detection_radius_body_exited(body: Node2D) -> void:
	var entity_id: int = body.get_instance_id()
	if far_enemies.has(entity_id):
		if debug: print(body.name, " is not detectable")
		far_enemies.erase(entity_id)


func get_near_enemies() -> Array:
	_lazy_entities_cleanup()
	return near_enemies.values()


func get_far_enemies() -> Array:
	_lazy_entities_cleanup()
	return far_enemies.values()



func _lazy_entities_cleanup() -> void:
	var current_frame: int = Engine.get_frames_drawn()
	if current_frame != _last_cleanup_frame:
		_entities_cleanup()
		_last_cleanup_frame = current_frame



func _entities_cleanup() -> void:
	for key in far_enemies.keys():
		if not is_instance_valid(far_enemies[key]):
			far_enemies.erase(key)

	for key in near_enemies.keys():
		if not is_instance_valid(near_enemies[key]):
			near_enemies.erase(key)
