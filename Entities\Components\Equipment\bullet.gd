extends Sprite2D

@onready var shadow: Sprite2D = $Shadow2D
@onready var animation_player: AnimationPlayer = $AnimationPlayer
@onready var distance_timeout: Timer = $DistanceTimeout
@onready var ray_cast_2d: RayCast2D = $RayCast2D
@onready var team_component: TeamComponent = $TeamComponent

var speed: float =  120.0
var attack       := Attack.new()


func _physics_process(delta: float) -> void:
	global_position += Vector2(1, 0).rotated(rotation) * speed * delta
	shadow.position = Vector2(-2, 2).rotated(-rotation)
	if ray_cast_2d.is_colliding():
		var collider := ray_cast_2d.get_collider()
		if not collider:
			return

		var hitboxC := collider as HitBoxComponent
		if not hitboxC:
			return

		var teamC: TeamComponent = hitboxC.get_parent().get_node_or_null("TeamComponent")
		if not teamC:
			return

		# prevent multiple collisions with same object
		ray_cast_2d.add_exception(collider)

		# prevent friendly damage
		if team_component.team == teamC.team:
			return

		hitboxC.hit_received.emit(attack)
		animation_player.play(&"remove")


func _on_animation_player_animation_finished(anim_name: StringName) -> void:
	if anim_name == &"remove":
		queue_free()


func _on_distance_timeout_timeout() -> void:
	animation_player.play(&"remove")
