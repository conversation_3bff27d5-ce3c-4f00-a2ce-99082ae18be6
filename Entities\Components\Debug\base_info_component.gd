@icon("res://assets/IconGodotNode/node_2D/icon_magnifier.png")
class_name BaseInfoComponent
extends Node2D

var generated := false
var labels: Array[Label] = []

## Handles logic behind value changes
func init(init_value: bool, change_signal: Signal):
	# Add labels on start if enabled
	if init_value:
		show_labels()
	
	# Add or remove labels on state change
	change_signal.connect(
		func(new_value: bool):
			if new_value:
				show_labels()
			else:
				hide_labels()
	)
	
	_fix_rotation.call_deferred()

func _fix_rotation():
	var parent := get_parent() as Node2D
	rotation = -parent.global_rotation

func _generate_labels():
	pass

func _add_labels():
	var y_offset = 0
	var spacing = 20  # space between labels
	for label in labels:
		label.position = Vector2(0, y_offset)
		label.z_index  = 10
		y_offset += spacing
		add_child(label)

func show_labels():
	if not generated:
		_generate_labels()
		_add_labels()
		generated = true
	
	for label in labels:
		label.visible = true

func hide_labels():
	for label in labels:
		label.visible = false
