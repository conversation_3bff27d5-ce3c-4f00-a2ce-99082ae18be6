@tool
extends BTAction

@export var array_a_var: StringName
@export var array_b_var: StringName
@export var array_c_var: StringName
@export var operation: Operation = Operation.UNION
enum Operation {
	UNION,
	INTERSECTION,
	DIFFERENCE_A_MINUS_B,
	DIFFERENCE_B_MINUS_A,
	SYMMETRIC_DIFFERENCE
}


static func get_operation_name(value):
	for key in Operation.keys():
		if Operation[key] == value:
			return key
	return str(value)


func _generate_name() -> String:
	var op_name = get_operation_name(operation)
	return "%s %s %s → %s" % [array_a_var, op_name, array_b_var, array_c_var]


func _tick(_delta: float) -> Status:
	var array_a: Array = blackboard.get_var(array_a_var)
	var array_b: Array = blackboard.get_var(array_b_var)

	if array_a == null or array_b == null:
		push_warning("One of the input arrays is null")
		return FAILURE

	var result: Array = []

	match operation:
		Operation.UNION:
			result = array_a.duplicate()
			result.append_array(array_b)
		Operation.INTERSECTION:
			for item in array_a:
				if array_b.has(item):
					result.append(item)
		Operation.DIFFERENCE_A_MINUS_B:
			for item in array_a:
				if not array_b.has(item):
					result.append(item)
		Operation.DIFFERENCE_B_MINUS_A:
			for item in array_b:
				if not array_a.has(item):
					result.append(item)
		Operation.SYMMETRIC_DIFFERENCE:
			for item in array_a:
				if not array_b.has(item):
					result.append(item)
			for item in array_b:
				if not array_a.has(item):
					result.append(item)
		_:
			push_warning("Unknown operation: %s" % str(operation))
			return FAILURE

	blackboard.set_var(array_c_var, result)
	return SUCCESS
