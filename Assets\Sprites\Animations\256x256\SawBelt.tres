[gd_resource type="SpriteFrames" load_steps=38 format=3 uid="uid://bidgxas13pikt"]

[ext_resource type="Texture2D" uid="uid://bjyi5wt8s7cqf" path="res://Assets/Sprites/SpriteSheets/1x1Saw_sprite_sheet_anim.png" id="1_h6dym"]

[sub_resource type="AtlasTexture" id="AtlasTexture_h6dym"]
atlas = ExtResource("1_h6dym")
region = Rect2(0, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_mo874"]
atlas = ExtResource("1_h6dym")
region = Rect2(256, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_0dixt"]
atlas = ExtResource("1_h6dym")
region = Rect2(512, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_hrunv"]
atlas = ExtResource("1_h6dym")
region = Rect2(768, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_omy83"]
atlas = ExtResource("1_h6dym")
region = Rect2(1024, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_ohtsc"]
atlas = ExtResource("1_h6dym")
region = Rect2(1280, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_g38y5"]
atlas = ExtResource("1_h6dym")
region = Rect2(1536, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_50ctk"]
atlas = ExtResource("1_h6dym")
region = Rect2(1792, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_sg7bj"]
atlas = ExtResource("1_h6dym")
region = Rect2(2048, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_vhtos"]
atlas = ExtResource("1_h6dym")
region = Rect2(0, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_3jtxa"]
atlas = ExtResource("1_h6dym")
region = Rect2(256, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_1f0fx"]
atlas = ExtResource("1_h6dym")
region = Rect2(512, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_vg478"]
atlas = ExtResource("1_h6dym")
region = Rect2(768, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_4tp0l"]
atlas = ExtResource("1_h6dym")
region = Rect2(1024, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_uu8u6"]
atlas = ExtResource("1_h6dym")
region = Rect2(1280, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_ctybg"]
atlas = ExtResource("1_h6dym")
region = Rect2(1536, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_xwxfw"]
atlas = ExtResource("1_h6dym")
region = Rect2(1792, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_ykmd3"]
atlas = ExtResource("1_h6dym")
region = Rect2(2048, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_p21ys"]
atlas = ExtResource("1_h6dym")
region = Rect2(0, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_3fqyc"]
atlas = ExtResource("1_h6dym")
region = Rect2(256, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_smw3p"]
atlas = ExtResource("1_h6dym")
region = Rect2(512, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_pk885"]
atlas = ExtResource("1_h6dym")
region = Rect2(768, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_0uhce"]
atlas = ExtResource("1_h6dym")
region = Rect2(1024, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_5sr68"]
atlas = ExtResource("1_h6dym")
region = Rect2(1280, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_gukai"]
atlas = ExtResource("1_h6dym")
region = Rect2(1536, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_itfvc"]
atlas = ExtResource("1_h6dym")
region = Rect2(1792, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_xdnka"]
atlas = ExtResource("1_h6dym")
region = Rect2(2048, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_j4tql"]
atlas = ExtResource("1_h6dym")
region = Rect2(0, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_43dqc"]
atlas = ExtResource("1_h6dym")
region = Rect2(256, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_ft4vh"]
atlas = ExtResource("1_h6dym")
region = Rect2(512, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_4r5nw"]
atlas = ExtResource("1_h6dym")
region = Rect2(768, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_hp5c1"]
atlas = ExtResource("1_h6dym")
region = Rect2(1024, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_mnjod"]
atlas = ExtResource("1_h6dym")
region = Rect2(1280, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_f8yk1"]
atlas = ExtResource("1_h6dym")
region = Rect2(1536, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_ac55y"]
atlas = ExtResource("1_h6dym")
region = Rect2(1792, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_j5sho"]
atlas = ExtResource("1_h6dym")
region = Rect2(2048, 256, 256, 256)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_h6dym")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mo874")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_0dixt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hrunv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_omy83")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ohtsc")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_g38y5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_50ctk")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_sg7bj")
}],
"loop": true,
"name": &"Down",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_vhtos")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3jtxa")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_1f0fx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vg478")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4tp0l")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_uu8u6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ctybg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xwxfw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ykmd3")
}],
"loop": true,
"name": &"Left",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_p21ys")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3fqyc")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_smw3p")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_pk885")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_0uhce")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5sr68")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gukai")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_itfvc")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xdnka")
}],
"loop": true,
"name": &"Right",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_j4tql")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_43dqc")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ft4vh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4r5nw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hp5c1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mnjod")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_f8yk1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ac55y")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_j5sho")
}],
"loop": true,
"name": &"Up",
"speed": 10.0
}]
