[gd_scene load_steps=5 format=3 uid="uid://duyts3roful8l"]

[ext_resource type="PackedScene" uid="uid://de6s4bwkw62cx" path="res://Entities/Buildings/Power Network/testing/testing_power_consumer.tscn" id="1_cq5q0"]
[ext_resource type="PackedScene" uid="uid://c5dgr7sr43otq" path="res://Entities/Buildings/Power Network/testing/testing_power_generator.tscn" id="2_bqxmk"]
[ext_resource type="PackedScene" uid="uid://c5hbk2wlqmp0g" path="res://Entities/Buildings/Power Network/testing/testing_power_battery.tscn" id="2_cq5q0"]
[ext_resource type="PackedScene" uid="uid://ms0scm876sot" path="res://Entities/Buildings/Power Network/power_network.tscn" id="4_cplcg"]

[node name="PowerGridTestScene" type="Node2D"]
position = Vector2(1, 0)

[node name="PowerConsumer" parent="." instance=ExtResource("1_cq5q0")]
position = Vector2(643, 328)

[node name="PowerBattery" parent="." instance=ExtResource("2_cq5q0")]
position = Vector2(366, 328)

[node name="PowerBattery2" parent="." instance=ExtResource("2_cq5q0")]
position = Vector2(366, 420)

[node name="PowerGenerator" parent="." instance=ExtResource("2_bqxmk")]
position = Vector2(90, 330)

[node name="PowerNetwork" parent="." instance=ExtResource("4_cplcg")]
