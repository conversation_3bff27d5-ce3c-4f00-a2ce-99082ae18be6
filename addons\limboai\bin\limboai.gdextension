[configuration]

entry_symbol = "limboai_init"
compatibility_minimum = "4.2"

[libraries]

macos.debug = "res://addons/limboai/bin/liblimboai.macos.editor.framework"
macos.release = "res://addons/limboai/bin/liblimboai.macos.template_release.framework"
windows.debug.x86_32 = "res://addons/limboai/bin/liblimboai.windows.editor.x86_32.dll"
windows.release.x86_32 = "res://addons/limboai/bin/liblimboai.windows.template_release.x86_32.dll"
windows.debug.x86_64 = "res://addons/limboai/bin/liblimboai.windows.editor.x86_64.dll"
windows.release.x86_64 = "res://addons/limboai/bin/liblimboai.windows.template_release.x86_64.dll"
linux.debug.x86_64 = "res://addons/limboai/bin/liblimboai.linux.editor.x86_64.so"
linux.release.x86_64 = "res://addons/limboai/bin/liblimboai.linux.template_release.x86_64.so"
linux.debug.arm64 = "res://addons/limboai/bin/liblimboai.linux.editor.arm64.so"
linux.release.arm64 = "res://addons/limboai/bin/liblimboai.linux.template_release.arm64.so"
linux.debug.rv64 = "res://addons/limboai/bin/liblimboai.linux.editor.rv64.so"
linux.release.rv64 = "res://addons/limboai/bin/liblimboai.linux.template_release.rv64.so"
android.debug.arm64 = "res://addons/limboai/bin/liblimboai.android.editor.arm64.so"
android.release.arm64 = "res://addons/limboai/bin/liblimboai.android.template_release.arm64.so"
android.debug.arm32 = "res://addons/limboai/bin/liblimboai.android.editor.arm32.so"
android.release.arm32 = "res://addons/limboai/bin/liblimboai.android.template_release.arm32.so"
android.debug.x86_64 = "res://addons/limboai/bin/liblimboai.android.editor.x86_64.so"
android.release.x86_64 = "res://addons/limboai/bin/liblimboai.android.template_release.x86_64.so"
android.debug.x86_32 = "res://addons/limboai/bin/liblimboai.android.editor.x86_32.so"
android.release.x86_32 = "res://addons/limboai/bin/liblimboai.android.template_release.x86_32.so"
ios.debug.arm64 = "res://addons/limboai/bin/liblimboai.ios.editor.arm64.dylib"
ios.release.arm64 = "res://addons/limboai/bin/liblimboai.ios.template_release.arm64.dylib"
ios.debug.simulator = "res://addons/limboai/bin/liblimboai.ios.editor.universal.dylib"
ios.release.simulator = "res://addons/limboai/bin/liblimboai.ios.template_release.universal.dylib"
web.debug.wasm32 = "res://addons/limboai/bin/liblimboai.web.editor.wasm32.wasm"
web.release.wasm32 = "res://addons/limboai/bin/liblimboai.web.template_release.wasm32.wasm"

[icons]

BTAction = "res://addons/limboai/icons/BTAction.svg"
BTAlwaysFail = "res://addons/limboai/icons/BTAlwaysFail.svg"
BTAlwaysSucceed = "res://addons/limboai/icons/BTAlwaysSucceed.svg"
BTAwaitAnimation = "res://addons/limboai/icons/BTAwaitAnimation.svg"
BTCallMethod = "res://addons/limboai/icons/BTCallMethod.svg"
BTCheckAgentProperty = "res://addons/limboai/icons/BTCheckAgentProperty.svg"
BTCheckTrigger = "res://addons/limboai/icons/BTCheckTrigger.svg"
BTCheckVar = "res://addons/limboai/icons/BTCheckVar.svg"
BTComment = "res://addons/limboai/icons/BTComment.svg"
BTCondition = "res://addons/limboai/icons/BTCondition.svg"
BTConsolePrint = "res://addons/limboai/icons/BTConsolePrint.svg"
BTCooldown = "res://addons/limboai/icons/BTCooldown.svg"
BTDecorator = "res://addons/limboai/icons/BTDecorator.svg"
BTDelay = "res://addons/limboai/icons/BTDelay.svg"
BTDynamicSelector = "res://addons/limboai/icons/BTDynamicSelector.svg"
BTDynamicSequence = "res://addons/limboai/icons/BTDynamicSequence.svg"
BTEvaluateExpression = "res://addons/limboai/icons/BTEvaluateExpression.svg"
BTFail = "res://addons/limboai/icons/BTFail.svg"
BTForEach = "res://addons/limboai/icons/BTForEach.svg"
BTInvert = "res://addons/limboai/icons/BTInvert.svg"
BTNewScope = "res://addons/limboai/icons/BTNewScope.svg"
BTParallel = "res://addons/limboai/icons/BTParallel.svg"
BTPauseAnimation = "res://addons/limboai/icons/BTPauseAnimation.svg"
BTPlayAnimation = "res://addons/limboai/icons/BTPlayAnimation.svg"
BTPlayer = "res://addons/limboai/icons/BTPlayer.svg"
BTProbability = "res://addons/limboai/icons/BTProbability.svg"
BTProbabilitySelector = "res://addons/limboai/icons/BTProbabilitySelector.svg"
BTRandomSelector = "res://addons/limboai/icons/BTRandomSelector.svg"
BTRandomSequence = "res://addons/limboai/icons/BTRandomSequence.svg"
BTRandomWait = "res://addons/limboai/icons/BTRandomWait.svg"
BTRepeat = "res://addons/limboai/icons/BTRepeat.svg"
BTRepeatUntilFailure = "res://addons/limboai/icons/BTRepeatUntilFailure.svg"
BTRepeatUntilSuccess = "res://addons/limboai/icons/BTRepeatUntilSuccess.svg"
BTRunLimit = "res://addons/limboai/icons/BTRunLimit.svg"
BTSelector = "res://addons/limboai/icons/BTSelector.svg"
BTSequence = "res://addons/limboai/icons/BTSequence.svg"
BTSetAgentProperty = "res://addons/limboai/icons/BTSetAgentProperty.svg"
BTSetVar = "res://addons/limboai/icons/BTSetVar.svg"
BTState = "res://addons/limboai/icons/BTState.svg"
BTStopAnimation = "res://addons/limboai/icons/BTStopAnimation.svg"
BTSubtree = "res://addons/limboai/icons/BTSubtree.svg"
BTTimeLimit = "res://addons/limboai/icons/BTTimeLimit.svg"
BTWait = "res://addons/limboai/icons/BTWait.svg"
BTWaitTicks = "res://addons/limboai/icons/BTWaitTicks.svg"
BehaviorTree = "res://addons/limboai/icons/BehaviorTree.svg"
BehaviorTreeView = "res://addons/limboai/icons/BehaviorTreeView.svg"
BlackboardPlan = "res://addons/limboai/icons/BlackboardPlan.svg"
LimboAI = "res://addons/limboai/icons/LimboAI.svg"
LimboDeselectAll = "res://addons/limboai/icons/LimboDeselectAll.svg"
LimboEditBlackboard = "res://addons/limboai/icons/LimboEditBlackboard.svg"
LimboExtraBlackboard = "res://addons/limboai/icons/LimboExtraBlackboard.svg"
LimboExtraClock = "res://addons/limboai/icons/LimboExtraClock.svg"
LimboExtraVariable = "res://addons/limboai/icons/LimboExtraVariable.svg"
LimboExtractSubtree = "res://addons/limboai/icons/LimboExtractSubtree.svg"
LimboHSM = "res://addons/limboai/icons/LimboHSM.svg"
LimboPercent = "res://addons/limboai/icons/LimboPercent.svg"
LimboSelectAll = "res://addons/limboai/icons/LimboSelectAll.svg"
LimboState = "res://addons/limboai/icons/LimboState.svg"
LimboVarAdd = "res://addons/limboai/icons/LimboVarAdd.svg"
LimboVarEmpty = "res://addons/limboai/icons/LimboVarEmpty.svg"
LimboVarError = "res://addons/limboai/icons/LimboVarError.svg"
LimboVarExists = "res://addons/limboai/icons/LimboVarExists.svg"
LimboVarNotFound = "res://addons/limboai/icons/LimboVarNotFound.svg"
LimboVarPrivate = "res://addons/limboai/icons/LimboVarPrivate.svg"
