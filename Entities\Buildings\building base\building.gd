class_name Building
extends Node2D


@export_category("Prefab Data")
## The reference to the resource that is setting the stats of current building.
@export var stats: BuildingStats
## The sprite of the building that shall be used mainly for icons in the UI.
@onready var sprite: Sprite2D = $Sprite2D
## An animation of the building that is showing that the building is in a process.
@onready var animation: AnimatedSprite2D = $AnimatedSprite2D
## The shape of the building collision
@onready var collision_shape: CollisionShape2D = $"Physics Body/CollisionShape2D"

@export_category("Runtime Data")
@export var tile_coordinates: Vector2i = Vector2i.ZERO
@export var components_node: Node2D

## Binds currently assigned stats to child nodes.[br]
## If we bind new stats we have to manually change them.
func bind_stats() -> void:
	# The loading of nodes has to be here, because otherwise some nodes might not be entirelly
	# ready and exception is going to be raised.
	sprite = $Sprite2D
	animation = $AnimatedSprite2D
	collision_shape = $"Physics Body/CollisionShape2D"
	components_node = $Components
	sprite.texture = stats.texture

	animation.sprite_frames = stats.animation_frames
	if animation.sprite_frames != null:
		sprite.hide()

	collision_shape.shape = stats.collision_shape
	BuildingSignalBus.building_built.connect(_on_building_built)
	
	# initialize all components
	for component in stats.component_scripts:
		var new_node = Node2D.new()
		new_node.script = component
		components_node.add_child(new_node)
		new_node.name = component.get_global_name()


## Method that is called by the builder at the moment of building a Building.[br]
## It is supposed to be implemented by child classes.
func on_build() -> void:
	pass


# TODO: this might not be needed
func connect_to_others() -> void:
	pass


# This has to be defined to connect it properly to some signals. At runtime the override defined
# by some child shall be used.
func _on_building_built(_building: Building) -> void:
	pass
