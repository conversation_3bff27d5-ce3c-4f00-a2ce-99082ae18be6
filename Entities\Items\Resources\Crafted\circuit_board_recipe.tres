[gd_resource type="Resource" script_class="ItemRecipe" load_steps=6 format=3 uid="uid://d07hmmji42lir"]

[ext_resource type="Script" uid="uid://dta70er7xbp1f" path="res://Entities/Items/item_data.gd" id="1_fkpdw"]
[ext_resource type="Resource" uid="uid://bws7gp7s3q8i6" path="res://Entities/Items/Resources/Crafted/copper_wire.tres" id="2_1r7ol"]
[ext_resource type="Script" uid="uid://kiuh738llx2t" path="res://Entities/Items/item_recipe.gd" id="2_8mlr5"]
[ext_resource type="Resource" uid="uid://b1ab8o4g5cyr4" path="res://Entities/Items/Resources/Processed/silicon.tres" id="3_gv7i1"]
[ext_resource type="Resource" uid="uid://cxch5jhgtuf81" path="res://Entities/Items/Resources/Crafted/circuit_board.tres" id="4_p4yh6"]

[resource]
resource_name = "Circuit Board"
script = ExtResource("2_8mlr5")
processing_building = 4
input_resources = Dictionary[ExtResource("1_fkpdw"), int]({
ExtResource("2_1r7ol"): 1,
ExtResource("3_gv7i1"): 1
})
output_resources = Dictionary[ExtResource("1_fkpdw"), int]({
ExtResource("4_p4yh6"): 1
})
processing_time = 0.0
unlocked_by_default = false
metadata/_custom_type_script = "uid://kiuh738llx2t"
