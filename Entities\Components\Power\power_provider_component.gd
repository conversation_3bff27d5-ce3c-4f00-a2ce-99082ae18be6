class_name PowerProviderComponent
extends PowerBaseComponent

## Power provided by component to power network
@warning_ignore("unused_signal")
signal power_provided(power, delta)

## How much of current output can be used right now
var efficiency: float = 0.0

## How much power this output can transfer
@export var power_amount: float = 10.0
## Power priority group of this output
@export var power_priority := PowerOutputPrioriy.Enum.GENERAL

func _ready() -> void:
	add_to_group(&"power_provider", true)

func _enter_tree() -> void:
	PlanetSignalBus.power_provider_added.emit(self)

func _exit_tree() -> void:
	PlanetSignalBus.power_provider_removed.emit(self)
