class_name ResearchInfo
extends Control

var extra_info_visible: bool = false
@onready var animation_player: AnimationPlayer = $ExtraInfo/AnimationPlayer

func _ready() -> void:
	PlanetSignalBus.planet_menu_opened.connect(_on_extra_info_opened)
	animation_player.play("RESET")

func show_hide() -> void:
	if extra_info_visible:
		_hide_menu()
	else:
		_show_menu()


func _show_menu() -> void:
	animation_player.play("ShowExtraInfo")
	extra_info_visible = true


func _hide_menu() -> void:
	animation_player.play_backwards("ShowExtraInfo")
	extra_info_visible = false


func _on_extra_info_opened(emitter: Node) -> void:
	if emitter == self or not extra_info_visible:
		return
	_hide_menu()


func _on_open_extra_info_button_pressed() -> void:
	PlanetSignalBus.planet_menu_opened.emit(self)
	show_hide()
