@tool
extends HBoxContainer

@onready var SELECTION_BAR_BUTTON = load("res://UI/Components/selection_bar_button.tscn")
var stats_to_scene: Dictionary = {}

func _ready() -> void:
	var unlocked_buildings_stats := UnlockManager.buildings_unlocked
	unlocked_buildings_stats.sort_custom(
		func(a: BuildingStats, b: BuildingStats) -> bool:
			return a.menu_order_priority < b.menu_order_priority
	)
	
	for building_stats in unlocked_buildings_stats:
		var button: SelectionBarButton = SELECTION_BAR_BUTTON.instantiate()
		button.stats = building_stats
		add_child(button)
