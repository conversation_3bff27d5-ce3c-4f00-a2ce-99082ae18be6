extends Panel


@onready var new_game_option: VBoxContainer = $NewGameOption
@onready var continue_game_option: VBoxContainer = $ContinueGameOption


func _ready() -> void:
	if SaveManager.has_save(name):
		continue_game_option.visible = true
		new_game_option.visible = false
	else:
		continue_game_option.visible = false
		new_game_option.visible = true


func _on_continue_button_pressed() -> void:
	SaveManager.selected_save_name = name
	SceneManager.to_space_map()
	
func _on_new_game_button_pressed() -> void:
	print("New game initialized")
	SaveManager.selected_save_name = name
	SceneManager.to_space_map()
	
func _play_button_click_sound() -> void:
	AudioManager.create_audio(SoundEffect.SOUND_EFFECT_TYPE.UI_BUTTON_CLICKED)
