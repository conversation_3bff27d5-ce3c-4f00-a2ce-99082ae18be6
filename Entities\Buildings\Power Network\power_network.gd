class_name PowerNetwork
extends Node2D

var providers_by_tier: Dictionary[int, Array] = {}
var receivers_by_tier: Dictionary[int, Array] = {}

signal power_recalculated(
	total_available_power: float,
	total_required_power: float,
	total_power_transferred: float,
	delta: float
)

func _ready() -> void:
	_collect_power_nodes()
	
	PlanetSignalBus.power_reciever_added.connect(
	func(reciever: PowerReceiverComponent):
		receivers_by_tier[reciever.power_priority].append(reciever)
	)
	PlanetSignalBus.power_reciever_removed.connect(
	func(reciever: PowerReceiverComponent):
		receivers_by_tier[reciever.power_priority].erase(reciever)
	)

	PlanetSignalBus.power_provider_added.connect(
		func(provider: PowerProviderComponent):
		providers_by_tier[provider.power_priority].append(provider)
	)	
	PlanetSignalBus.power_provider_removed.connect(
		func(provider: PowerProviderComponent):
		providers_by_tier[provider.power_priority].erase(provider)
	)

# Collects power providers and receivers from the scene tree and sorts them by priority tiers.
func _collect_power_nodes() -> void:
	providers_by_tier.clear()
	receivers_by_tier.clear()

	var tree = get_tree()
	var provider_nodes := tree.get_nodes_in_group("power_provider")
	var receiver_nodes := tree.get_nodes_in_group("power_receiver")

	# Initialize dictionaries with keys for all priority tiers
	for tier in PowerInputPrioriy.Enum.values():
		providers_by_tier[tier] = []
	for tier in PowerOutputPrioriy.Enum.values():
		receivers_by_tier[tier] = []

	# Sort providers into tiers
	for provider: PowerProviderComponent in provider_nodes:
		providers_by_tier[provider.power_priority].append(provider)

	# Sort receivers into tiers
	for receiver: PowerReceiverComponent in receiver_nodes:
		receivers_by_tier[receiver.power_priority].append(receiver)


func _process(delta: float) -> void:
	var available_power_per_tier: Dictionary[int, float] = {}
	var required_power_per_tier: Dictionary[int, float] = {}

	var total_available_power := _calculate_available_power(available_power_per_tier)
	var total_required_power := _calculate_required_power(required_power_per_tier)

	var receiver_power_allocations: Dictionary[PowerReceiverComponent,float] = {}
	var provider_power_allocations: Dictionary[PowerProviderComponent,float] = {}

	var total_power_transferred = 0.0

	total_power_transferred = _distribute_power(
		available_power_per_tier,
		required_power_per_tier,
		receiver_power_allocations,
		provider_power_allocations
	)

	_notify_power_changes(provider_power_allocations, receiver_power_allocations, delta)
	power_recalculated.emit(total_available_power, total_required_power, total_power_transferred, delta)


func _calculate_available_power(available_power_per_tier: Dictionary) -> float:
	var total_available_power := 0.0
	for tier in providers_by_tier.keys():
		var power_sum = 0.0
		for provider: PowerProviderComponent in providers_by_tier[tier]:
			power_sum += provider.power_amount * provider.efficiency

		available_power_per_tier[tier] = power_sum
		total_available_power += power_sum
	return total_available_power


func _calculate_required_power(required_power_per_tier: Dictionary) -> float:
	var total_required_power: float = 0
	for tier in receivers_by_tier.keys():
		var power_sum: float = 0.0
		for receiver: PowerReceiverComponent in receivers_by_tier[tier]:
			power_sum += receiver.power_amount * receiver.efficiency

		required_power_per_tier[tier] = power_sum
		total_required_power += power_sum
	return total_required_power


func _distribute_power(
	available_power_per_tier: Dictionary[int, float],
	required_power_per_tier: Dictionary[int, float],
	receiver_power_allocations: Dictionary[PowerReceiverComponent,float],
	provider_power_allocations: Dictionary[PowerProviderComponent,float]
	) -> float:

	var power_transferred = 0.0
	var current_provider_tier = PowerOutputPrioriy.Enum.values()[0]
	var power_available_in_current_tier = available_power_per_tier.get(current_provider_tier, 0.0)

	for receiver_tier in receivers_by_tier.keys():
		if current_provider_tier >= PowerOutputPrioriy.Enum.values()[-1]:
			break

		var power_required_in_receiver_tier = required_power_per_tier.get(receiver_tier, 0.0)
		var starting_provider_tier = current_provider_tier

		# Increase provider tier until we have enough power or reach max tier
		while power_required_in_receiver_tier > power_available_in_current_tier \
			and current_provider_tier < PowerOutputPrioriy.Enum.values()[-1]:

			# Prevents battery from to other backup sorces
			if receiver_tier == PowerInputPrioriy.Enum.BATTERY and power_required_in_receiver_tier >= PowerOutputPrioriy.Enum.BATTERY:
				break

			current_provider_tier += 1
			power_available_in_current_tier += available_power_per_tier.get(current_provider_tier, 0.0)

		var spread_ratio = min(power_available_in_current_tier / power_required_in_receiver_tier, 1.0)
		if spread_ratio <= 0:
			break

		var power_to_deliver = 0.0
		for receiver: PowerReceiverComponent in receivers_by_tier[receiver_tier]:
			var allocated_power = receiver.power_amount * receiver.efficiency * spread_ratio
			receiver_power_allocations[receiver] = allocated_power
			power_to_deliver += allocated_power

		power_available_in_current_tier -= power_to_deliver
		power_transferred += power_to_deliver

		# Assign power drawn to providers across tiers that cover this delivery
		for tier in range(starting_provider_tier, current_provider_tier + 1):
			var tier_providers = providers_by_tier[tier]

			# Spread load across all providers in last tier
			var tier_spread_ratio = 1.0
			if starting_provider_tier != current_provider_tier and tier == current_provider_tier:
				tier_spread_ratio = power_to_deliver / available_power_per_tier[tier]

			for provider: PowerProviderComponent in tier_providers:
				if power_to_deliver <= 0:
					break

				var power_providable = provider.power_amount * provider.efficiency * tier_spread_ratio
				power_providable -= provider_power_allocations.get(provider, 0.0)
				power_providable = min(power_providable, power_to_deliver)

				provider_power_allocations[provider] = provider_power_allocations.get(provider, 0.0) + power_providable
				power_to_deliver -= power_providable

	return power_transferred


func _notify_power_changes(
	provider_power_allocations: Dictionary,
	receiver_power_allocations: Dictionary,
	delta: float
	) -> void:

	if DebugManager.power_debug:
		# Sends also zero power notifications
		for provider_tier in providers_by_tier.keys():
			for provider: PowerProviderComponent in providers_by_tier.get(provider_tier,[]):
				provider.power_provided.emit(provider_power_allocations.get(provider,0), delta)

		for receiver_tier in receivers_by_tier.keys():
			for receiver: PowerReceiverComponent in receivers_by_tier.get(receiver_tier,[]):
				receiver.power_received.emit(receiver_power_allocations.get(receiver,0), delta)
	else:
		# Skips zero power notifications
		for provider in provider_power_allocations.keys():
			provider.power_provided.emit(provider_power_allocations.get(provider,0), delta)

		for receiver in receiver_power_allocations.keys():
			receiver.power_received.emit(receiver_power_allocations.get(receiver,0), delta)
