extends Node2D

@onready var power_provider_component: PowerProviderComponent = $PowerProviderComponent

func _ready() -> void:
	power_provider_component.efficiency = 1.0
	power_provider_component.power_provided.connect(_on_power_provided)


## Respond to power draw by setting the animation speed to a percentage of
## the power taken vs amount of power it's able to output
func _on_power_provided(power: Variant, _delta: Variant) -> void:
	var _generator_load: float = power / power_provider_component.power_amount
