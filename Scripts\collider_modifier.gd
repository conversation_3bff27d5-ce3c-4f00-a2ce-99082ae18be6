extends TileMapLayer

@onready var foreground: TileMapLayer = $"../Foreground"


func _use_tile_data_runtime_update(coords: Vector2i) -> bool:
	if coords in foreground.get_used_cells():
		return true

	return false


func _tile_data_runtime_update(coords: Vector2i, tile_data: TileData) -> void:
	if coords in foreground.get_used_cells():
		var cell_data = foreground.get_cell_tile_data(coords)
		var nav_polygon = cell_data.get_navigation_polygon(0)
		tile_data.set_navigation_polygon(0, nav_polygon)
		return
