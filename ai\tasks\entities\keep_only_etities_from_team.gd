@tool
extends BTAction

@export var entities_var := &"entities"
@export var team_var: Team.Enum


func _generate_name() -> String:
	return "Keep only %s from team %s" % [entities_var, Team.to_name(team_var)]


func _tick(_delta: float) -> Status:
	var entities: Array = blackboard.get_var(entities_var, null)
	if entities == null or entities.is_empty():
		return FAILURE

	var filtered := []

	for entity in entities:
		var team_component: TeamComponent = entity.get_node_or_null("TeamComponent")
		if team_component and team_component.team == team_var:
			filtered.append(entity)

	blackboard.set_var(entities_var, filtered)
	return SUCCESS
