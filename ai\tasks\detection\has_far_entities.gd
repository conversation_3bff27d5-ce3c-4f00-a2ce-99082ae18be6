@tool
extends BTCondition

var npc: DetectionComponent


func _generate_name() -> String:
	return "Check if there are near entities"


func _setup() -> void:
	npc = agent.get_node_or_null("DetectionComponent")
	if npc == null:
		push_error("Detection_Coponent node not found as a child of the parent!")


func _tick(_delta: float) -> Status:
	if npc and npc.far_enemies.size() > 0:
		return SUCCESS
	return FAILURE
