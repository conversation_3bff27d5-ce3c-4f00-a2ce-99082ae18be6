[gd_resource type="SpriteFrames" load_steps=18 format=3 uid="uid://deq1e81m0rmy"]

[ext_resource type="Texture2D" uid="uid://brii45dcrn5vi" path="res://Assets/Sprites/Planet_HighDef_sprite_sheet_anim.png" id="1_wqlf5"]

[sub_resource type="AtlasTexture" id="AtlasTexture_3a8h3"]
atlas = ExtResource("1_wqlf5")
region = Rect2(0, 0, 300, 300)

[sub_resource type="AtlasTexture" id="AtlasTexture_aemf8"]
atlas = ExtResource("1_wqlf5")
region = Rect2(0, 300, 300, 300)

[sub_resource type="AtlasTexture" id="AtlasTexture_cod2m"]
atlas = ExtResource("1_wqlf5")
region = Rect2(0, 600, 300, 300)

[sub_resource type="AtlasTexture" id="AtlasTexture_76y2v"]
atlas = ExtResource("1_wqlf5")
region = Rect2(0, 900, 300, 300)

[sub_resource type="AtlasTexture" id="AtlasTexture_dytu4"]
atlas = ExtResource("1_wqlf5")
region = Rect2(0, 1200, 300, 300)

[sub_resource type="AtlasTexture" id="AtlasTexture_i8cx4"]
atlas = ExtResource("1_wqlf5")
region = Rect2(0, 1500, 300, 300)

[sub_resource type="AtlasTexture" id="AtlasTexture_g1tit"]
atlas = ExtResource("1_wqlf5")
region = Rect2(0, 1800, 300, 300)

[sub_resource type="AtlasTexture" id="AtlasTexture_h61kf"]
atlas = ExtResource("1_wqlf5")
region = Rect2(0, 2100, 300, 300)

[sub_resource type="AtlasTexture" id="AtlasTexture_chut8"]
atlas = ExtResource("1_wqlf5")
region = Rect2(0, 2400, 300, 300)

[sub_resource type="AtlasTexture" id="AtlasTexture_q600x"]
atlas = ExtResource("1_wqlf5")
region = Rect2(0, 2700, 300, 300)

[sub_resource type="AtlasTexture" id="AtlasTexture_ldw2s"]
atlas = ExtResource("1_wqlf5")
region = Rect2(0, 3000, 300, 300)

[sub_resource type="AtlasTexture" id="AtlasTexture_8xcyq"]
atlas = ExtResource("1_wqlf5")
region = Rect2(0, 3300, 300, 300)

[sub_resource type="AtlasTexture" id="AtlasTexture_cap7d"]
atlas = ExtResource("1_wqlf5")
region = Rect2(0, 3600, 300, 300)

[sub_resource type="AtlasTexture" id="AtlasTexture_08se0"]
atlas = ExtResource("1_wqlf5")
region = Rect2(0, 3900, 300, 300)

[sub_resource type="AtlasTexture" id="AtlasTexture_c8cwy"]
atlas = ExtResource("1_wqlf5")
region = Rect2(0, 4200, 300, 300)

[sub_resource type="AtlasTexture" id="AtlasTexture_557sa"]
atlas = ExtResource("1_wqlf5")
region = Rect2(0, 4500, 300, 300)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_3a8h3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_aemf8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_cod2m")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_76y2v")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_dytu4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_i8cx4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_g1tit")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_h61kf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_chut8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_q600x")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ldw2s")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8xcyq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_cap7d")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_08se0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_c8cwy")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_557sa")
}],
"loop": true,
"name": &"default",
"speed": 10.0
}]
