extends StaticBody2D

@onready var team_component: TeamComponent = $TeamComponent
@onready var bt_player: BTPlayer = $BTPlayer

func _on_spawner_component_unit_spawned(unit: Node) -> void:
	var unitTeam: TeamComponent = unit.get_node_or_null("TeamComponent")
	if unitTeam and team_component:
		unitTeam.team = team_component.team

	var unitBTPlayer: BTPlayer = unit.get_node_or_null("BTPlayer")
	if unitBTPlayer and bt_player:
		unitBTPlayer.blackboard.set_parent(bt_player.blackboard)
