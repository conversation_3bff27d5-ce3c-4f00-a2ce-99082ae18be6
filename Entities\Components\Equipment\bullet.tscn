[gd_scene load_steps=9 format=3 uid="uid://dm8yc1clcpyaa"]

[ext_resource type="Texture2D" uid="uid://bum0tkri32g6o" path="res://Assets/IconGodotNode/node_2D/icon_bullet.png" id="1_ohb0n"]
[ext_resource type="Script" uid="uid://oa3um0l58mkh" path="res://Entities/Components/General/shadow.gd" id="2_gpl3w"]
[ext_resource type="Script" uid="uid://cvdcocc4kbcll" path="res://Entities/Components/Equipment/bullet.gd" id="2_u8w5u"]
[ext_resource type="PackedScene" uid="uid://sgsrhtv7bywd" path="res://Entities/Components/team_component.tscn" id="4_ft458"]

[sub_resource type="Animation" id="Animation_u8w5u"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath(".:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(1, 1, 1, 1)]
}

[sub_resource type="Animation" id="Animation_gpl3w"]
resource_name = "appear"
length = 0.2
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.2),
"transitions": PackedFloat32Array(0.5, 2),
"update": 0,
"values": [Vector2(1e-05, 1e-05), Vector2(1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath(".:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.2),
"transitions": PackedFloat32Array(0.5, 2),
"update": 0,
"values": [Color(1, 1, 1, 0), Color(1, 1, 1, 1)]
}

[sub_resource type="Animation" id="Animation_ft458"]
resource_name = "remove"
length = 0.2
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.2),
"transitions": PackedFloat32Array(0.5, 2),
"update": 0,
"values": [Vector2(1, 1), Vector2(0, 0)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath(".:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.2),
"transitions": PackedFloat32Array(0.5, 2),
"update": 0,
"values": [Color(1, 1, 1, 1), Color(1, 1, 1, 0)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_ft458"]
_data = {
&"RESET": SubResource("Animation_u8w5u"),
&"appear": SubResource("Animation_gpl3w"),
&"remove": SubResource("Animation_ft458")
}

[node name="Bullet" type="Sprite2D"]
texture = ExtResource("1_ohb0n")
script = ExtResource("2_u8w5u")

[node name="Shadow2D" type="Sprite2D" parent="."]
modulate = Color(0, 0, 0, 1)
z_index = -1
texture = ExtResource("1_ohb0n")
script = ExtResource("2_gpl3w")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_ft458")
}
autoplay = "appear"
speed_scale = 2.0

[node name="RayCast2D" type="RayCast2D" parent="."]
target_position = Vector2(8, 0)
collision_mask = 2
hit_from_inside = true
collide_with_areas = true
collide_with_bodies = false

[node name="DistanceTimeout" type="Timer" parent="."]
wait_time = 10.0
one_shot = true
autostart = true

[node name="TeamComponent" parent="." instance=ExtResource("4_ft458")]

[connection signal="animation_finished" from="AnimationPlayer" to="." method="_on_animation_player_animation_finished"]
[connection signal="timeout" from="DistanceTimeout" to="." method="_on_distance_timeout_timeout"]
