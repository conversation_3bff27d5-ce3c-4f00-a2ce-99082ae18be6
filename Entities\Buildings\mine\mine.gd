class_name Mine
extends ConveyorBasedBuilding


# TODO: think about better approach then absolute path
@onready var resource_tilemap: TileMapLayer = $/root/PlanetSurface/Tiles/Resources
# TODO: the timer might be part of all item handling buildings
@onready var timer: Timer = $Timer

# TODO: rename to ItemStats
var item_data: ItemData


func on_build() -> void:
	var data: TileData = resource_tilemap.get_cell_tile_data(tile_coordinates)
	if data:
		var resource_kind: StringName = data.get_custom_data("Resource Kind")
		if resource_kind == null:
			return
		item_data = ResourceManager.STRING_NAME_TO_ITEM_DATA_MAP[resource_kind]
		# wait_time = 0 would lead to infinite waiting
		if timer.wait_time != 0.0:
			timer.start()


func _ready() -> void:
	timer.wait_time = 3.0
	timer.timeout.connect(_on_timer_timeout)


func _after_push_item(_item: Item) -> void:
	timer.start()


func _on_timer_timeout() -> void:
	_held_item = _instantiate_item(item_data)
