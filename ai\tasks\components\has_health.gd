@tool
extends BTCondition

enum Enum{
	above,
	equal,
	bellow,
}


static func to_name(value: Enum) -> StringName:
	for key in Enum:
		if Enum[key] == value:
			return key
	return str(value)

@export var operation: Enum
@export var value: float

var npc: HealthComponent


func _generate_name() -> String:
	return "Check if health is %s %s" % [to_name(operation), value]


func _setup() -> void:
	npc = agent.get_node_or_null("HealthComponent")
	if npc == null:
		push_error("HealthComponent node not found as a child of the parent!")


func _tick(_delta: float) -> Status:
	if npc.health > value:
		return SUCCESS
	return FAILURE
