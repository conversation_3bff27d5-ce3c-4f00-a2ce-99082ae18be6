@tool
extends BTAction

var npc: NPC

@export var target_var = &"target_pos"
@export var min_radius = 50
@export var max_radius = 100


func _generate_name() -> String:
	return "Set random target position to %s in radius (%s,%s)" % [target_var, min_radius, max_radius]


func _setup() -> void:
	npc = agent as NPC



func _tick(_delta: float) -> Status:
	var current_position: Vector2 = npc.global_position
	var offset: float             = randf_range(50.0, 100.0)
	var newTarget: Vector2        = generate_random_target(current_position, offset)

	blackboard.set_var(target_var, newTarget)
	return SUCCESS



func generate_random_target(current_position: Vector2, offset: float) -> Vector2:
	var dir: float               = randf_range(0, 2 * PI)
	var new_destination: Vector2 = Vector2(
									   current_position.x + cos(dir) * offset,
									   current_position.y + sin(dir) * offset
								   )
	return new_destination
