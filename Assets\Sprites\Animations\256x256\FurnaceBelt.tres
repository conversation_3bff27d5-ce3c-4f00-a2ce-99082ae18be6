[gd_resource type="SpriteFrames" load_steps=38 format=3 uid="uid://8y7oliaxg0wc"]

[ext_resource type="Texture2D" uid="uid://bpd8xde3fip87" path="res://Assets/Sprites/SpriteSheets/1x1Furnace_sprite_sheet_anim.png" id="1_dofpx"]

[sub_resource type="AtlasTexture" id="AtlasTexture_dofpx"]
atlas = ExtResource("1_dofpx")
region = Rect2(0, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_lk4bq"]
atlas = ExtResource("1_dofpx")
region = Rect2(256, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_aaa05"]
atlas = ExtResource("1_dofpx")
region = Rect2(512, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_cts16"]
atlas = ExtResource("1_dofpx")
region = Rect2(768, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_5n3jb"]
atlas = ExtResource("1_dofpx")
region = Rect2(1024, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_kx3xv"]
atlas = ExtResource("1_dofpx")
region = Rect2(1280, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_87gx7"]
atlas = ExtResource("1_dofpx")
region = Rect2(1536, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_dvpue"]
atlas = ExtResource("1_dofpx")
region = Rect2(1792, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_0ikio"]
atlas = ExtResource("1_dofpx")
region = Rect2(2048, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_f0xyv"]
atlas = ExtResource("1_dofpx")
region = Rect2(0, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_rfmys"]
atlas = ExtResource("1_dofpx")
region = Rect2(256, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_e7031"]
atlas = ExtResource("1_dofpx")
region = Rect2(512, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_wcsx5"]
atlas = ExtResource("1_dofpx")
region = Rect2(768, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_h1nkg"]
atlas = ExtResource("1_dofpx")
region = Rect2(1024, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_kxamv"]
atlas = ExtResource("1_dofpx")
region = Rect2(1280, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_4u8pw"]
atlas = ExtResource("1_dofpx")
region = Rect2(1536, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_6terr"]
atlas = ExtResource("1_dofpx")
region = Rect2(1792, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_tg41b"]
atlas = ExtResource("1_dofpx")
region = Rect2(2048, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_joykm"]
atlas = ExtResource("1_dofpx")
region = Rect2(0, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_p5050"]
atlas = ExtResource("1_dofpx")
region = Rect2(256, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_cgncl"]
atlas = ExtResource("1_dofpx")
region = Rect2(512, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_pbxbd"]
atlas = ExtResource("1_dofpx")
region = Rect2(768, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_5g8p3"]
atlas = ExtResource("1_dofpx")
region = Rect2(1024, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_64cyv"]
atlas = ExtResource("1_dofpx")
region = Rect2(1280, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_30nvh"]
atlas = ExtResource("1_dofpx")
region = Rect2(1536, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_8iyv5"]
atlas = ExtResource("1_dofpx")
region = Rect2(1792, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_1u1bn"]
atlas = ExtResource("1_dofpx")
region = Rect2(2048, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_mq2ed"]
atlas = ExtResource("1_dofpx")
region = Rect2(0, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_vecp0"]
atlas = ExtResource("1_dofpx")
region = Rect2(256, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_lhp0o"]
atlas = ExtResource("1_dofpx")
region = Rect2(512, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_nwm4c"]
atlas = ExtResource("1_dofpx")
region = Rect2(768, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_7x40i"]
atlas = ExtResource("1_dofpx")
region = Rect2(1024, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_i2g0w"]
atlas = ExtResource("1_dofpx")
region = Rect2(1280, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_prrmf"]
atlas = ExtResource("1_dofpx")
region = Rect2(1536, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_wuipt"]
atlas = ExtResource("1_dofpx")
region = Rect2(1792, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_o87bi"]
atlas = ExtResource("1_dofpx")
region = Rect2(2048, 256, 256, 256)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_dofpx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_lk4bq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_aaa05")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_cts16")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5n3jb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kx3xv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_87gx7")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_dvpue")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_0ikio")
}],
"loop": true,
"name": &"Down",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_f0xyv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rfmys")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_e7031")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wcsx5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_h1nkg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kxamv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4u8pw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6terr")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tg41b")
}],
"loop": true,
"name": &"Left",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_joykm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_p5050")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_cgncl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_pbxbd")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5g8p3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_64cyv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_30nvh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8iyv5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_1u1bn")
}],
"loop": true,
"name": &"Right",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_mq2ed")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vecp0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_lhp0o")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_nwm4c")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7x40i")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_i2g0w")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_prrmf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wuipt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_o87bi")
}],
"loop": true,
"name": &"Up",
"speed": 10.0
}]
