[gd_resource type="ShaderMaterial" load_steps=4 format=3 uid="uid://bc3n5r7ecf8f1"]

[ext_resource type="Shader" uid="uid://74vlwwh7qhcm" path="res://Shaders/StarBackground.gdshader" id="1_v0pgh"]

[sub_resource type="FastNoiseLite" id="FastNoiseLite_eso2y"]
noise_type = 0
frequency = 0.3395

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_ywgur"]
noise = SubResource("FastNoiseLite_eso2y")

[resource]
shader = ExtResource("1_v0pgh")
shader_parameter/resolution = Vector2(600, 400)
shader_parameter/noise_texture = SubResource("NoiseTexture2D_ywgur")
shader_parameter/density = 72.3
shader_parameter/speed_x = 1.8
shader_parameter/speed_y = 1.49012e-07
shader_parameter/layers = 5.0
