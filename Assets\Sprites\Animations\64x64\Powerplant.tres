[gd_resource type="SpriteFrames" load_steps=14 format=3 uid="uid://btjmd6fwiovoc"]

[ext_resource type="Texture2D" uid="uid://dwrlemvpyaro" path="res://Assets/Sprites/32x32/SpriteSheets/PowerPlant_sprite_sheet_anim.png" id="1_3ptkp"]

[sub_resource type="AtlasTexture" id="AtlasTexture_6j7th"]
atlas = ExtResource("1_3ptkp")
region = Rect2(0, 192, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_ljipo"]
atlas = ExtResource("1_3ptkp")
region = Rect2(64, 192, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_h0n0r"]
atlas = ExtResource("1_3ptkp")
region = Rect2(128, 192, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_4gklm"]
atlas = ExtResource("1_3ptkp")
region = Rect2(0, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_p3s6v"]
atlas = ExtResource("1_3ptkp")
region = Rect2(64, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_xetfa"]
atlas = ExtResource("1_3ptkp")
region = Rect2(128, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_oqltn"]
atlas = ExtResource("1_3ptkp")
region = Rect2(0, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_kdaab"]
atlas = ExtResource("1_3ptkp")
region = Rect2(64, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_ct4ke"]
atlas = ExtResource("1_3ptkp")
region = Rect2(128, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_ysdck"]
atlas = ExtResource("1_3ptkp")
region = Rect2(0, 64, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_q3wvk"]
atlas = ExtResource("1_3ptkp")
region = Rect2(64, 64, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_b2age"]
atlas = ExtResource("1_3ptkp")
region = Rect2(128, 64, 64, 64)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_6j7th")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ljipo")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_h0n0r")
}],
"loop": false,
"name": &"East",
"speed": 9.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_4gklm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_p3s6v")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xetfa")
}],
"loop": false,
"name": &"North",
"speed": 9.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_oqltn")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kdaab")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ct4ke")
}],
"loop": false,
"name": &"South",
"speed": 9.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_ysdck")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_q3wvk")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_b2age")
}],
"loop": false,
"name": &"West",
"speed": 9.0
}]
