[gd_scene load_steps=14 format=3 uid="uid://b87b5to830rh3"]

[ext_resource type="Script" uid="uid://72t24d8b3ien" path="res://Scenes/Menus/main_menu.gd" id="1_koqhg"]
[ext_resource type="Texture2D" uid="uid://cl53aoj4xvhkj" path="res://Assets/Sprites/Backgoundblurred.png" id="2_cmrfp"]
[ext_resource type="Script" uid="uid://c10qwfspws40c" path="res://Scripts/background.gd" id="3_loo7a"]
[ext_resource type="Texture2D" uid="uid://okuk61tfb31d" path="res://Assets/Sprites/Titel.png" id="4_if7li"]
[ext_resource type="StyleBox" uid="uid://3hc1e36ppnyp" path="res://Assets/Styles/MainMenuButtonHover.tres" id="5_rrcx7"]
[ext_resource type="StyleBox" uid="uid://frjs5kyq7sj8" path="res://Assets/Styles/MainMenuButtonClicked.tres" id="6_fh7cp"]
[ext_resource type="StyleBox" uid="uid://xgn5cty7bog3" path="res://Assets/Styles/MainMenuButton.tres" id="7_8e205"]
[ext_resource type="Script" uid="uid://b62vw83ypymjm" path="res://UI/Components/menu_button.gd" id="8_7467j"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_cqjrg"]
texture = ExtResource("2_cmrfp")
modulate_color = Color(0.647395, 0.647395, 0.647395, 1)

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_e2xy7"]
texture = ExtResource("4_if7li")

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_iwfq2"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_7r1g0"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_cqjrg"]

[node name="MainMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_koqhg")

[node name="Background" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -521.0
offset_top = -293.0
offset_right = 520.0
offset_bottom = 293.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxTexture_cqjrg")
script = ExtResource("3_loo7a")

[node name="TextureRectB" type="TextureRect" parent="Background"]
self_modulate = Color(1, 1, 1, 0)
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="TextureRectA" type="TextureRect" parent="Background"]
self_modulate = Color(1, 1, 1, 0)
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 217.0
offset_top = -35.0
offset_right = 217.0
offset_bottom = -35.0
grow_horizontal = 2
grow_vertical = 2

[node name="Title" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -326.0
offset_right = 326.0
offset_bottom = 179.0
grow_horizontal = 2
theme_override_styles/panel = SubResource("StyleBoxTexture_e2xy7")

[node name="StartButton" type="Button" parent="."]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -225.0
offset_top = -196.0
offset_bottom = -143.0
grow_horizontal = 0
grow_vertical = 0
pivot_offset = Vector2(225, 26.5)
size_flags_horizontal = 8
size_flags_vertical = 3
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_colors/font_pressed_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 3
theme_override_font_sizes/font_size = 32
theme_override_styles/focus = SubResource("StyleBoxEmpty_iwfq2")
theme_override_styles/hover = ExtResource("5_rrcx7")
theme_override_styles/pressed = ExtResource("6_fh7cp")
theme_override_styles/normal = ExtResource("7_8e205")
text = "  Start game"
script = ExtResource("8_7467j")

[node name="SettingsButton" type="Button" parent="."]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -180.0
offset_top = -133.0
offset_bottom = -80.0
grow_horizontal = 0
grow_vertical = 0
pivot_offset = Vector2(180, 26.5)
size_flags_horizontal = 8
size_flags_vertical = 3
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_colors/font_pressed_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 3
theme_override_font_sizes/font_size = 32
theme_override_styles/focus = SubResource("StyleBoxEmpty_7r1g0")
theme_override_styles/hover = ExtResource("5_rrcx7")
theme_override_styles/pressed = ExtResource("6_fh7cp")
theme_override_styles/normal = ExtResource("7_8e205")
text = "  Settings
"
script = ExtResource("8_7467j")

[node name="QuitButton" type="Button" parent="."]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -123.0
offset_top = -70.0
offset_bottom = -17.0
grow_horizontal = 0
grow_vertical = 0
pivot_offset = Vector2(123, 26.5)
size_flags_horizontal = 8
size_flags_vertical = 3
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_colors/font_pressed_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 3
theme_override_font_sizes/font_size = 32
theme_override_styles/focus = SubResource("StyleBoxEmpty_cqjrg")
theme_override_styles/hover = ExtResource("5_rrcx7")
theme_override_styles/pressed = ExtResource("6_fh7cp")
theme_override_styles/normal = ExtResource("7_8e205")
text = "  Quit
"
script = ExtResource("8_7467j")

[connection signal="pressed" from="StartButton" to="." method="_on_start_button_pressed"]
[connection signal="pressed" from="SettingsButton" to="." method="_on_settings_button_pressed"]
[connection signal="pressed" from="QuitButton" to="." method="_on_quit_button_pressed"]
