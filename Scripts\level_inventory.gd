extends Node2D

class_name ResourceInventory

@export var resources: Dictionary[ItemType.Enum,int]

func _ready() -> void:
	for item in ItemType.get_storable_items():
		if item not in resources:
			resources[item]=0


func add_resource(amount: int, type: ItemType.Enum ) -> void:
	if type in resources:
		resources[type]+=amount
		

func consume_resources(cost: Dictionary)-> bool:
	if !sufficient_resources(cost):
		return false
	for item in cost.keys():
		resources[item]-=cost[item]
	return true


func sufficient_resources(cost: Dictionary) -> bool:
	#print('sufficiency check')
	#print(resources)
	
	for raw_key in cost.keys():
		#NOTE somehow scuffed fix
		var item: ItemType.Enum = int(raw_key)
		if item not in resources:
			print('unknown resource ', item)

			return false
		if cost[item] > resources[item]:
			#print('cost ',cost )
			#print('res ', resources)
			print('not enough resources')
			return false
	return true
