extends Node2D

var sound_effect_dict: Dictionary = {} ## Loads all registered SoundEffects on ready as a reference.

@export var sound_effects: Array[SoundEffect] ## Stores all possible SoundEffects that can be played.


func _ready() -> void:
	for sound_effect: SoundEffect in sound_effects:
		sound_effect_dict[sound_effect.type] = sound_effect


func create_audio(type: SoundEffect.SOUND_EFFECT_TYPE) -> void:
	if sound_effect_dict.has(type):
		var sound_effect: SoundEffect = sound_effect_dict[type]
		var sound: AudioStreamPlayer = AudioStreamPlayer.new()
		add_child(sound)
		sound.stream = sound_effect.sound_effect
		sound.volume_db = sound_effect.volume
		sound.pitch_scale = sound_effect.pitch_scale
		sound.pitch_scale += randf_range(-sound_effect.pitch_randomness, sound_effect.pitch_randomness )
		sound.finished.connect(sound.queue_free)
		sound.play()
