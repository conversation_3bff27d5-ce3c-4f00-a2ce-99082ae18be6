[gd_resource type="SpriteFrames" load_steps=38 format=3 uid="uid://c2s04yeymjsar"]

[ext_resource type="Texture2D" uid="uid://dns757ugeuxln" path="res://Assets/Sprites/SpriteSheets/FourMergeNewSmall_sprite_sheet_anim.png" id="1_qx3lp"]

[sub_resource type="AtlasTexture" id="AtlasTexture_ur3hw"]
atlas = ExtResource("1_qx3lp")
region = Rect2(0, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_205p4"]
atlas = ExtResource("1_qx3lp")
region = Rect2(256, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_wnd2c"]
atlas = ExtResource("1_qx3lp")
region = Rect2(512, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_kpgm5"]
atlas = ExtResource("1_qx3lp")
region = Rect2(768, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_oygpa"]
atlas = ExtResource("1_qx3lp")
region = Rect2(1024, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_hnur2"]
atlas = ExtResource("1_qx3lp")
region = Rect2(1280, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_vawmh"]
atlas = ExtResource("1_qx3lp")
region = Rect2(1536, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_xs687"]
atlas = ExtResource("1_qx3lp")
region = Rect2(1792, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_ebdjx"]
atlas = ExtResource("1_qx3lp")
region = Rect2(2048, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_waa2u"]
atlas = ExtResource("1_qx3lp")
region = Rect2(0, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_5sknf"]
atlas = ExtResource("1_qx3lp")
region = Rect2(256, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_4hb36"]
atlas = ExtResource("1_qx3lp")
region = Rect2(512, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_awoi7"]
atlas = ExtResource("1_qx3lp")
region = Rect2(768, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_02iuv"]
atlas = ExtResource("1_qx3lp")
region = Rect2(1024, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_42lfu"]
atlas = ExtResource("1_qx3lp")
region = Rect2(1280, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_bfkag"]
atlas = ExtResource("1_qx3lp")
region = Rect2(1536, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_vwbhl"]
atlas = ExtResource("1_qx3lp")
region = Rect2(1792, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_ynut2"]
atlas = ExtResource("1_qx3lp")
region = Rect2(2048, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_qa1ex"]
atlas = ExtResource("1_qx3lp")
region = Rect2(0, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_3hw4p"]
atlas = ExtResource("1_qx3lp")
region = Rect2(256, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_rdda5"]
atlas = ExtResource("1_qx3lp")
region = Rect2(512, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_frmrf"]
atlas = ExtResource("1_qx3lp")
region = Rect2(768, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_vrjod"]
atlas = ExtResource("1_qx3lp")
region = Rect2(1024, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_dawty"]
atlas = ExtResource("1_qx3lp")
region = Rect2(1280, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_ppdt0"]
atlas = ExtResource("1_qx3lp")
region = Rect2(1536, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_7ugfe"]
atlas = ExtResource("1_qx3lp")
region = Rect2(1792, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_jhj26"]
atlas = ExtResource("1_qx3lp")
region = Rect2(2048, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_mgl3s"]
atlas = ExtResource("1_qx3lp")
region = Rect2(0, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_tnsm6"]
atlas = ExtResource("1_qx3lp")
region = Rect2(256, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_83r34"]
atlas = ExtResource("1_qx3lp")
region = Rect2(512, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_rxr4s"]
atlas = ExtResource("1_qx3lp")
region = Rect2(768, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_bsbr1"]
atlas = ExtResource("1_qx3lp")
region = Rect2(1024, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_iqg3g"]
atlas = ExtResource("1_qx3lp")
region = Rect2(1280, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_e5oe8"]
atlas = ExtResource("1_qx3lp")
region = Rect2(1536, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_hyxt4"]
atlas = ExtResource("1_qx3lp")
region = Rect2(1792, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_gbyxv"]
atlas = ExtResource("1_qx3lp")
region = Rect2(2048, 256, 256, 256)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_ur3hw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_205p4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wnd2c")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kpgm5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_oygpa")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hnur2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vawmh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xs687")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ebdjx")
}],
"loop": true,
"name": &"Down",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_waa2u")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5sknf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4hb36")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_awoi7")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_02iuv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_42lfu")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bfkag")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vwbhl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ynut2")
}],
"loop": true,
"name": &"Left",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_qa1ex")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3hw4p")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rdda5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_frmrf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vrjod")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_dawty")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ppdt0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7ugfe")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_jhj26")
}],
"loop": true,
"name": &"Right",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_mgl3s")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tnsm6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_83r34")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rxr4s")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bsbr1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_iqg3g")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_e5oe8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hyxt4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gbyxv")
}],
"loop": true,
"name": &"Up",
"speed": 10.0
}]
