extends Node

@warning_ignore("unused_signal")
signal building_built(building: Building)

@warning_ignore("unused_signal")
signal building_demolished(building: Building)

## triggered by selection button on hud when switching to building mode
@warning_ignore("unused_signal")
signal building_mode_triggered()

## triggered by selection button on hud when switching to demolition mode
@warning_ignore("unused_signal")
signal demolition_mode_triggered()

## Sink consumed is called when a sink recieves and then deletes an item
## It also contains the data of what is the current throughput at that moment
## as well as the ID of the sink which is later used to differentiate the source
@warning_ignore("unused_signal")
signal sink_consumed(item_type: ItemType.Enum, throughput: int, sink_id: int)

@warning_ignore("unused_signal")
signal player_won()
