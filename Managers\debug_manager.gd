extends Node

## Cause network to announce zero power recieved/provided
## Also shows all network infos
var power_debug := false
signal power_debug_changed(new_value: bool)

var item_debug := false
signal item_debug_changed(new_value: bool)

func _unhandled_input(event: InputEvent) -> void:
	if event.is_action_pressed(&"debug_power"):
		power_debug = not power_debug
		power_debug_changed.emit(power_debug)
	
	if event.is_action_pressed(&"item_debug"):
		item_debug = not item_debug
		item_debug_changed.emit(item_debug)
