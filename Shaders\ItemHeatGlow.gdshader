shader_type canvas_item;

const float min_temp = 600.0;
const vec3 black_color = vec3(0.0, 0.0, 0.0);

const float red_temp = 800.0;
const vec3 red_color = vec3(0.7, 0.0, 0.0);

const float orange_temp = 1000.0;
const vec3 orange_color = vec3(1.0, 0.5, 0.0);

const float yellow_temp = 1200.0;
const vec3 yellow_color = vec3(1.0, 1.0, 0.0);

const float max_temp = 1600.0;
const vec3 white_color = vec3(1.0, 1.0, 1.0);

uniform float temperature : hint_range(0, 1600);

// Key color control points (blackbody glow)
vec3 color_gradient(float t) {
    if (t < min_temp)
		// No glow
        return black_color; 
    else if (t < red_temp)
		// Black to red
        return mix(black_color, red_color, (t-min_temp)/(red_temp-min_temp));
    else if (t < orange_temp)
		// Red to orange
        return mix(red_color, orange_color, (t-red_temp)/(orange_temp-red_temp));
    else if (t < yellow_temp)
		// Orange to yellow
        return mix(orange_color, yellow_color, (t-orange_temp)/(yellow_temp-orange_temp));
    else if (t < max_temp)
		// Yellow to white
        return mix(yellow_color, white_color, (t-yellow_temp)/(max_temp-yellow_temp));
	else
		// Pure white
		return white_color;
}

void fragment() {
	float norm_temp = clamp((temperature - min_temp) / (max_temp - min_temp), 0.0, 1.0);
    vec3 heat_color = color_gradient(temperature);
	
    COLOR = texture(TEXTURE, UV);
    COLOR.rgb = mix(COLOR.rgb, heat_color, norm_temp);
	COLOR.a *= norm_temp;
}
