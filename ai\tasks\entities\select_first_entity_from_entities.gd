@tool
extends BTAction

@export var entities_var := &"entities"
@export var selected_entity_var := &"selected_entity"


func _generate_name() -> String:
	return "Selects first entity from %s to %s" % [entities_var, selected_entity_var]



func _tick(_delta: float) -> Status:
	var entities: Array = blackboard.get_var(entities_var, null)
	if entities == null or entities.size() == 0:
		return FAILURE

	var entity: Node2D = entities.front()
	blackboard.set_var(selected_entity_var, entity)
	return SUCCESS
