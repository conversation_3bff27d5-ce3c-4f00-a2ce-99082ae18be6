@tool
class_name PowerInfoComponent
extends BaseInfoComponent
## Adds power info to all power components

func _ready() -> void:
	# Always show labels in editor
	if Engine.is_editor_hint():
		show_labels()
		return

	init(DebugManager.power_debug, DebugManager.power_debug_changed)

func _generate_labels():
	var providers : Array[PowerProviderComponent] = []
	var receivers : Array[PowerReceiverComponent] = []
	var buffers   : Array[PowerBufferComponent] = []

	for child in get_parent().get_children():
		if child is PowerProviderComponent:
			providers.append(child)
		if child is PowerReceiverComponent:
			receivers.append(child)
		if child is PowerBufferComponent:
			buffers.append(child)

	var parent := get_parent()
	
	var main_label := Label.new()
	main_label.text = parent.name
	labels.append(main_label)
	
	# Code to execute when in game.
	if parent is PowerNetwork:
		var network_label := Label.new()
		network_label.text = "P: %.1f R: %.1f T: %.1f NOT UPDATED" % [0, 0, 0]
		if not Engine.is_editor_hint():
			parent.power_recalculated.connect(
				func(total_available_power: float, total_required_power: float, total_power_transferred: float, _delta: float):
					network_label.text = "P: %.1f R: %.1f T: %.1f" % [total_available_power, total_required_power, total_power_transferred]
			)
		labels.append(network_label)

	for buffer: PowerBufferComponent in buffers:
		var battery_label := Label.new()
		battery_label.text = "C: %.1f / %.1f NOT UPDATED" % [0, buffer.max_storage]
		if not Engine.is_editor_hint():
			buffer.stored_energy_changed.connect(
				func():
					battery_label.text = "C: %.1f / %.1f" % [buffer.stored_power, buffer.max_storage]
			)
		labels.append(battery_label)
	
	for provider: PowerProviderComponent in providers:
		var provider_label := Label.new()
		provider_label.text = "P: %.1f / %.1f NOT UPDATED" % [0, provider.power_amount]
		if not Engine.is_editor_hint():
			provider.power_provided.connect(
				func(power: float, _delta: float):
					provider_label.text = "P: %.1f / %.1f" % [power, provider.power_amount]
			)
		labels.append(provider_label)

	for receiver: PowerReceiverComponent in receivers:
		var receiver_label := Label.new()
		receiver_label.text = "R: %.1f / %.1f NOT UPDATED" % [0, receiver.power_amount]
		if not Engine.is_editor_hint():
			receiver.power_received.connect(
				func(power: float, _delta: float):
					receiver_label.text = "R: %.1f / %.1f" % [power, receiver.power_amount]
			)
		labels.append(receiver_label)
