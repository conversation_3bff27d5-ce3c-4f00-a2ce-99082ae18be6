class_name ResultsUI
extends Control

func _ready() -> void:
	BuildingSignalBus.player_won.connect(display_results)
	hide()


func display_results() -> void:
	# TODO do more than just show the result screen
	show()


func _on_continue_button_pressed() -> void:
	_play_button_click_sound()
	hide()


func _on_back_button_pressed() -> void:
	SceneManager.to_space_map()


func _play_button_click_sound() -> void:
	AudioManager.create_audio(SoundEffect.SOUND_EFFECT_TYPE.UI_BUTTON_CLICKED)
