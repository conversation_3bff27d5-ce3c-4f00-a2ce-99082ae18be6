[gd_scene load_steps=5 format=3 uid="uid://c6301xv3brdpt"]

[ext_resource type="Shader" uid="uid://74vlwwh7qhcm" path="res://Shaders/StarBackground.gdshader" id="1_6jp55"]

[sub_resource type="FastNoiseLite" id="FastNoiseLite_6jp55"]
noise_type = 0
frequency = 0.303

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_gtllh"]
noise = SubResource("FastNoiseLite_6jp55")

[sub_resource type="ShaderMaterial" id="ShaderMaterial_gtllh"]
shader = ExtResource("1_6jp55")
shader_parameter/resolution = Vector2(600, 400)
shader_parameter/noise_texture = SubResource("NoiseTexture2D_gtllh")
shader_parameter/density = 69.7
shader_parameter/speed_x = 2.0
shader_parameter/speed_y = 0.0
shader_parameter/layers = 5.0

[node name="StarBackground" type="Node2D"]

[node name="ColorRect" type="ColorRect" parent="."]
material = SubResource("ShaderMaterial_gtllh")
offset_right = 600.0
offset_bottom = 400.0
