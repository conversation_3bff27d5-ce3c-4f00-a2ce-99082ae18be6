extends Node

@warning_ignore("unused_signal")
signal planet_menu_opened(emitter: Node)

@warning_ignore("unused_signal")
signal power_reciever_added(reciever: PowerReceiverComponent)

@warning_ignore("unused_signal")
signal power_provider_removed(provider: PowerProviderComponent)

@warning_ignore("unused_signal")
signal power_provider_added(provider: PowerProviderComponent)

@warning_ignore("unused_signal")
signal power_reciever_removed(reciever: PowerReceiverComponent)
