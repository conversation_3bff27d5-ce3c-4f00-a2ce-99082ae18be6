extends Node2D

@onready var BULLET = load("res://Entities/Components/Equipment/bullet.tscn")

@onready var shadow: Sprite2D = $Sprite2D/Shadow2D
@onready var shoot_pos: Marker2D = $Sprite2D/shoot_pos
@onready var shoot_timer: Timer = $ShootTimer

var time_between_shot := 0.25
var can_shoot         := true
var teamC: TeamComponent
signal shot_bullet(bullet_node: Node)

func _ready() -> void:
	shoot_timer.wait_time = time_between_shot

func _enter_tree() -> void:
	teamC = get_tree().get_first_node_in_group("Entity").get_node_or_null("TeamComponent")

func _physics_process(delta: float) -> void:
	var aim_direction = get_global_mouse_position() - shoot_pos.global_position
	var rotation_diff = aim_direction.angle()

	# Detect if the character is flipped
	var is_flipped = get_parent().scale.x < 0  # Use global_scale to account for parent scale
	if is_flipped:
		rotation_diff = PI - rotation_diff  # Mirror the angle when flipped

	rotation = lerp_angle(rotation, rotation_diff, 6.5 * delta)
	shadow.position = Vector2(-2, -2).rotated(-rotation)

	if Input.is_action_pressed(&"shoot") and can_shoot:
		_shoot()
		can_shoot = false
		shoot_timer.start()


func _shoot():
	var new_bullet = BULLET.instantiate()
	new_bullet.global_position = shoot_pos.global_position
	new_bullet.global_rotation = shoot_pos.global_rotation

	var bullet_teamC: TeamComponent = new_bullet.get_node_or_null("TeamComponent")
	bullet_teamC.team = teamC.team

	get_tree().root.add_child(new_bullet)

	emit_signal(&"shot_bullet", new_bullet)


func _on_shoot_timer_timeout() -> void:
	can_shoot = true
