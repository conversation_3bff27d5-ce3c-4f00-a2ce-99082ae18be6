class_name ItemHandlingBuildingStats
extends BuildingStats

@export_category("Conveyor Network Related Data")
## Input directions of a building.
@export_flags ("Front", "Back", "Left", "Right") var input_directions: int
## Output directions of a building.
@export_flags ("Front", "Back", "Left", "Right") var output_directions: int
# TODO: Currently, it is not clear how we are going to handle the speed of conveyors, etc.
# It could also be used to handle the processing time.
## The speed of transportation by a part of network.
@export var transport_speed: float
