class_name HealthComponent
extends Node2D

@export var MAX_HEALTH: float

@onready var custom_bar: CustomBar = $CustomBar
@onready var hit_box_component: HitBoxComponent = $"../HitBoxComponent"

var health: float

func _ready() -> void:
	health = MAX_HEALTH
	custom_bar._setup_bar(health)

	if hit_box_component:
		hit_box_component.connect(&"hit_received", processAttack)

func processAttack(attack: Attack):
	health -= attack.attack_damage
	custom_bar.change_value(health)

	if health <= 0:
		get_parent().queue_free()
