[gd_scene load_steps=58 format=3 uid="uid://de74akkj4ypnl"]

[ext_resource type="Script" uid="uid://cyi58fturxksa" path="res://Entities/Enemy/enemy.gd" id="1_bgbm4"]
[ext_resource type="Texture2D" uid="uid://badgm8ob0xbwo" path="res://Assets/IconGodotNode/control/icon_beetle.png" id="2_3l5gm"]
[ext_resource type="Script" uid="uid://dm7em6ko6muei" path="res://ai/tasks/movement/move_order_to_target.gd" id="3_fcg00"]
[ext_resource type="Script" uid="uid://cvsoda347ktvf" path="res://ai/tasks/detection/get_far_entities.gd" id="4_j7s4j"]
[ext_resource type="Script" uid="uid://btm0xkmdx1qx0" path="res://ai/tasks/entities/selected_entity_to_target_pos.gd" id="4_k11i2"]
[ext_resource type="Script" uid="uid://dt33nokpn4r85" path="res://ai/tasks/movement/set_random_target.gd" id="4_y11p7"]
[ext_resource type="Script" uid="uid://bewxs5r8yxglc" path="res://ai/tasks/movement/move_to_target.gd" id="5_bycej"]
[ext_resource type="Script" uid="uid://cpr45ln08spl1" path="res://ai/tasks/entities/keep_only_etities_from_team.gd" id="6_ej5u7"]
[ext_resource type="Script" uid="uid://d3ptecj5l0b07" path="res://ai/tasks/entities/is_entity_in_array.gd" id="6_j7s4j"]
[ext_resource type="Script" uid="uid://dsy2lidk1f02d" path="res://ai/tasks/entities/sort_entites_by_distance.gd" id="6_k11i2"]
[ext_resource type="Script" uid="uid://crlg0rjvgpxpg" path="res://ai/tasks/detection/get_near_entities.gd" id="6_oe1wn"]
[ext_resource type="PackedScene" uid="uid://d1thcq78aauo5" path="res://Entities/Components/AI/detection_coponent.tscn" id="6_y11p7"]
[ext_resource type="PackedScene" uid="uid://cvogo0j7ferfa" path="res://Entities/Components/health_component.tscn" id="7_bycej"]
[ext_resource type="Script" uid="uid://cmccvacqm3vhw" path="res://ai/tasks/entities/select_first_entity_from_entities.gd" id="7_r7nea"]
[ext_resource type="PackedScene" uid="uid://dhvj5lqfiuttb" path="res://Entities/Components/hit_box_component.tscn" id="8_a077d"]
[ext_resource type="Script" uid="uid://yovbo5vgtpyd" path="res://ai/tasks/entities/array_operation.gd" id="10_6pwl2"]
[ext_resource type="Script" uid="uid://q1vtvfby6cgv" path="res://ai/tasks/entities/clear_selected_entity.gd" id="13_liejx"]
[ext_resource type="PackedScene" uid="uid://q1qryefelgpb" path="res://Entities/Components/AI/navigation_component.tscn" id="14_oe1wn"]
[ext_resource type="PackedScene" uid="uid://sgsrhtv7bywd" path="res://Entities/Components/team_component.tscn" id="15_r7nea"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_s7bn3"]
prefetch_nodepath_vars = false
var/entities/name = &"entities"
var/entities/type = 28
var/entities/value = []
var/entities/hint = 0
var/entities/hint_string = ""
var/selected_entity/name = &"selected_entity"
var/selected_entity/type = 24
var/selected_entity/hint = 0
var/selected_entity/hint_string = ""
var/target_pos/name = &"target_pos"
var/target_pos/type = 5
var/target_pos/value = Vector2(0, 0)
var/target_pos/hint = 0
var/target_pos/hint_string = ""

[sub_resource type="BTAction" id="BTAction_qp131"]
script = ExtResource("3_fcg00")
target_var = &"target_pos"

[sub_resource type="BTAction" id="BTAction_ej5u7"]
script = ExtResource("5_bycej")
target_var = &"target_pos"

[sub_resource type="BTTimeLimit" id="BTTimeLimit_6pwl2"]
time_limit = 2.0
children = [SubResource("BTAction_ej5u7")]

[sub_resource type="BTSequence" id="BTSequence_j7s4j"]
children = [SubResource("BTAction_qp131"), SubResource("BTTimeLimit_6pwl2")]

[sub_resource type="BTAction" id="BTAction_6qm45"]
script = ExtResource("4_j7s4j")
entities_var = &"entities"

[sub_resource type="BTAction" id="BTAction_53dlj"]
script = ExtResource("10_6pwl2")
array_a_var = &"entities"
array_b_var = &"hive_entities_far"
array_c_var = &"entities"
operation = 1

[sub_resource type="BTCondition" id="BTCondition_liejx"]
script = ExtResource("6_j7s4j")
target_var = &"selected_entity"
entities_var = &"entities"

[sub_resource type="BTAction" id="BTAction_6pwl2"]
script = ExtResource("4_k11i2")
selected_entity_var = &"selected_entity"
target_pos_var = &"target_pos"

[sub_resource type="BTSequence" id="BTSequence_liejx"]
custom_name = "Chase"
children = [SubResource("BTAction_6qm45"), SubResource("BTAction_53dlj"), SubResource("BTCondition_liejx"), SubResource("BTAction_6pwl2")]

[sub_resource type="BTAction" id="BTAction_cwjay"]
script = ExtResource("4_j7s4j")
entities_var = &"entities"

[sub_resource type="BTAction" id="BTAction_1dgwf"]
script = ExtResource("10_6pwl2")
array_a_var = &"entities"
array_b_var = &"hive_entities_near"
array_c_var = &"entities"
operation = 1

[sub_resource type="BTAction" id="BTAction_j7s4j"]
script = ExtResource("6_ej5u7")
entities_var = &"entities"
team_var = 1

[sub_resource type="BTAction" id="BTAction_oe1wn"]
script = ExtResource("6_k11i2")
entities_var = &"entities"

[sub_resource type="BTAction" id="BTAction_vopru"]
script = ExtResource("7_r7nea")
entities_var = &"entities"
selected_entity_var = &"selected_entity"

[sub_resource type="BTAction" id="BTAction_liejx"]
script = ExtResource("4_k11i2")
selected_entity_var = &"selected_entity"
target_pos_var = &"target_pos"

[sub_resource type="BTSequence" id="BTSequence_x02pd"]
custom_name = "Choose engage target"
children = [SubResource("BTAction_cwjay"), SubResource("BTAction_1dgwf"), SubResource("BTAction_j7s4j"), SubResource("BTAction_oe1wn"), SubResource("BTAction_vopru"), SubResource("BTAction_liejx")]

[sub_resource type="BTAction" id="BTAction_w8wrk"]
script = ExtResource("6_oe1wn")
entities_var = &"entities"

[sub_resource type="BTAction" id="BTAction_7cvol"]
script = ExtResource("10_6pwl2")
array_a_var = &"entities"
array_b_var = &"hive_entities_far"
array_c_var = &"entities"
operation = 1

[sub_resource type="BTAction" id="BTAction_x02pd"]
script = ExtResource("6_ej5u7")
entities_var = &"entities"
team_var = 1

[sub_resource type="BTAction" id="BTAction_ir6ve"]
script = ExtResource("6_k11i2")
entities_var = &"entities"

[sub_resource type="BTAction" id="BTAction_pjdph"]
script = ExtResource("7_r7nea")
entities_var = &"entities"
selected_entity_var = &"selected_entity"

[sub_resource type="BTAction" id="BTAction_xdpq2"]
script = ExtResource("4_k11i2")
selected_entity_var = &"selected_entity"
target_pos_var = &"target_pos"

[sub_resource type="BTSequence" id="BTSequence_53dlj"]
custom_name = "Choose engage target"
children = [SubResource("BTAction_w8wrk"), SubResource("BTAction_7cvol"), SubResource("BTAction_x02pd"), SubResource("BTAction_ir6ve"), SubResource("BTAction_pjdph"), SubResource("BTAction_xdpq2")]

[sub_resource type="BTAction" id="BTAction_lk1wr"]
script = ExtResource("4_y11p7")
target_var = &"target_pos"
min_radius = 50
max_radius = 100

[sub_resource type="BTAction" id="BTAction_aw07j"]
script = ExtResource("13_liejx")
selected_entity_var = &"selected_entity"

[sub_resource type="BTSequence" id="BTSequence_pw70n"]
children = [SubResource("BTAction_lk1wr"), SubResource("BTAction_aw07j")]

[sub_resource type="BTSelector" id="BTSelector_ej5u7"]
children = [SubResource("BTSequence_liejx"), SubResource("BTSequence_x02pd"), SubResource("BTSequence_53dlj"), SubResource("BTSequence_pw70n")]

[sub_resource type="BTAction" id="BTAction_2nxwx"]
script = ExtResource("5_bycej")
target_var = &"target_pos"

[sub_resource type="BTTimeLimit" id="BTTimeLimit_k11i2"]
time_limit = 2.0
children = [SubResource("BTAction_2nxwx")]

[sub_resource type="BTSequence" id="BTSequence_0p0qh"]
children = [SubResource("BTSelector_ej5u7"), SubResource("BTTimeLimit_k11i2")]

[sub_resource type="BTSelector" id="BTSelector_ir6ve"]
children = [SubResource("BTSequence_j7s4j"), SubResource("BTSequence_0p0qh")]

[sub_resource type="BehaviorTree" id="BehaviorTree_2hac7"]
blackboard_plan = SubResource("BlackboardPlan_s7bn3")
root_task = SubResource("BTSelector_ir6ve")

[sub_resource type="BlackboardPlan" id="BlackboardPlan_6pwl2"]
prefetch_nodepath_vars = false

[sub_resource type="CircleShape2D" id="CircleShape2D_gqquq"]

[sub_resource type="CircleShape2D" id="CircleShape2D_bycej"]
radius = 36.0

[sub_resource type="CircleShape2D" id="CircleShape2D_a077d"]
radius = 103.044

[sub_resource type="CircleShape2D" id="CircleShape2D_y11p7"]
radius = 11.0

[node name="Enemy" type="CharacterBody2D" node_paths=PackedStringArray("navigation") groups=["Entity"]]
motion_mode = 1
wall_min_slide_angle = 0.0
script = ExtResource("1_bgbm4")
navigation = NodePath("NavigationComponent")

[node name="BTPlayer" type="BTPlayer" parent="."]
behavior_tree = SubResource("BehaviorTree_2hac7")
blackboard_plan = SubResource("BlackboardPlan_6pwl2")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_gqquq")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_3l5gm")

[node name="DetectionComponent" parent="." node_paths=PackedStringArray("nearArea", "farArea") instance=ExtResource("6_y11p7")]
nearArea = NodePath("Inner")
farArea = NodePath("Outer")

[node name="Inner" type="Area2D" parent="DetectionComponent"]
collision_layer = 0
collision_mask = 3

[node name="CollisionShape2D" type="CollisionShape2D" parent="DetectionComponent/Inner"]
shape = SubResource("CircleShape2D_bycej")
debug_color = Color(1, 0.5, 0, 0.1)

[node name="Outer" type="Area2D" parent="DetectionComponent"]
collision_layer = 0
collision_mask = 3

[node name="CollisionShape2D" type="CollisionShape2D" parent="DetectionComponent/Outer"]
shape = SubResource("CircleShape2D_a077d")
debug_color = Color(0.2, 1, 0, 0.1)

[node name="HitBoxComponent" parent="." instance=ExtResource("8_a077d")]

[node name="CollisionShape2D" type="CollisionShape2D" parent="HitBoxComponent"]
shape = SubResource("CircleShape2D_y11p7")
debug_color = Color(0, 0.641006, 0.341158, 0.42)

[node name="HealthComponent" parent="." instance=ExtResource("7_bycej")]
position = Vector2(0, -12)

[node name="NavigationComponent" parent="." instance=ExtResource("14_oe1wn")]
path_postprocessing = 1

[node name="TeamComponent" parent="." instance=ExtResource("15_r7nea")]
