@tool
extends BTAction

@export var selected_entity_var := &"selected_entity"
@export var target_pos_var := &"target_pos"


func _generate_name() -> String:
	return "Sets position of %s to %s" % [selected_entity_var, target_pos_var]



func _tick(_delta: float) -> Status:
	var entity: Node2D = blackboard.get_var(selected_entity_var, null)
	if entity == null:
		return FAILURE

	var entity_pos: Vector2 = entity.get_global_position()
	blackboard.set_var(target_pos_var, entity_pos)
	return SUCCESS
