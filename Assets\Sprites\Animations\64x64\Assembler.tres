[gd_resource type="SpriteFrames" load_steps=38 format=3 uid="uid://fw6adbajlg0s"]

[ext_resource type="Texture2D" uid="uid://7pcwr3stjf1" path="res://Assets/Sprites/32x32/SpriteSheets/Assembler_sprite_sheet_anim.png" id="1_6qeao"]

[sub_resource type="AtlasTexture" id="AtlasTexture_07ssc"]
atlas = ExtResource("1_6qeao")
region = Rect2(0, 192, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_a7ixm"]
atlas = ExtResource("1_6qeao")
region = Rect2(64, 192, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_epc2l"]
atlas = ExtResource("1_6qeao")
region = Rect2(128, 192, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_hjx63"]
atlas = ExtResource("1_6qeao")
region = Rect2(192, 192, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_rvqgb"]
atlas = ExtResource("1_6qeao")
region = Rect2(256, 192, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_iod5g"]
atlas = ExtResource("1_6qeao")
region = Rect2(320, 192, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_vcyl6"]
atlas = ExtResource("1_6qeao")
region = Rect2(384, 192, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_qyn3m"]
atlas = ExtResource("1_6qeao")
region = Rect2(448, 192, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_lqwwe"]
atlas = ExtResource("1_6qeao")
region = Rect2(512, 192, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_2hcfd"]
atlas = ExtResource("1_6qeao")
region = Rect2(0, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_lfuyc"]
atlas = ExtResource("1_6qeao")
region = Rect2(64, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_xl4wt"]
atlas = ExtResource("1_6qeao")
region = Rect2(128, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_1jrve"]
atlas = ExtResource("1_6qeao")
region = Rect2(192, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_86vm1"]
atlas = ExtResource("1_6qeao")
region = Rect2(256, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_7gcha"]
atlas = ExtResource("1_6qeao")
region = Rect2(320, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_di4dl"]
atlas = ExtResource("1_6qeao")
region = Rect2(384, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_hwah2"]
atlas = ExtResource("1_6qeao")
region = Rect2(448, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_5d01x"]
atlas = ExtResource("1_6qeao")
region = Rect2(512, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_7egum"]
atlas = ExtResource("1_6qeao")
region = Rect2(0, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_ri7r4"]
atlas = ExtResource("1_6qeao")
region = Rect2(64, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_rl3ul"]
atlas = ExtResource("1_6qeao")
region = Rect2(128, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_jmkhr"]
atlas = ExtResource("1_6qeao")
region = Rect2(192, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_5yd8a"]
atlas = ExtResource("1_6qeao")
region = Rect2(256, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_n4lmv"]
atlas = ExtResource("1_6qeao")
region = Rect2(320, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_aw2tg"]
atlas = ExtResource("1_6qeao")
region = Rect2(384, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_wnpm3"]
atlas = ExtResource("1_6qeao")
region = Rect2(448, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_hit8w"]
atlas = ExtResource("1_6qeao")
region = Rect2(512, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_ivi0c"]
atlas = ExtResource("1_6qeao")
region = Rect2(0, 64, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_h4jji"]
atlas = ExtResource("1_6qeao")
region = Rect2(64, 64, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_soovw"]
atlas = ExtResource("1_6qeao")
region = Rect2(128, 64, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_550fg"]
atlas = ExtResource("1_6qeao")
region = Rect2(192, 64, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_a5acn"]
atlas = ExtResource("1_6qeao")
region = Rect2(256, 64, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_mqysx"]
atlas = ExtResource("1_6qeao")
region = Rect2(320, 64, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_m88bt"]
atlas = ExtResource("1_6qeao")
region = Rect2(384, 64, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_pgg87"]
atlas = ExtResource("1_6qeao")
region = Rect2(448, 64, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_vt2so"]
atlas = ExtResource("1_6qeao")
region = Rect2(512, 64, 64, 64)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_07ssc")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_a7ixm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_epc2l")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hjx63")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rvqgb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_iod5g")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vcyl6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qyn3m")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_lqwwe")
}],
"loop": true,
"name": &"East",
"speed": 9.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_2hcfd")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_lfuyc")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xl4wt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_1jrve")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_86vm1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7gcha")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_di4dl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hwah2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5d01x")
}],
"loop": true,
"name": &"North",
"speed": 9.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_7egum")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ri7r4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rl3ul")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_jmkhr")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5yd8a")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_n4lmv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_aw2tg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wnpm3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hit8w")
}],
"loop": true,
"name": &"South",
"speed": 9.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_ivi0c")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_h4jji")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_soovw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_550fg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_a5acn")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mqysx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_m88bt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_pgg87")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vt2so")
}],
"loop": true,
"name": &"West",
"speed": 9.0
}]
