[gd_scene load_steps=11 format=3 uid="uid://dgkw5hc7wcs0k"]

[ext_resource type="StyleBox" uid="uid://b40l6cfagx5kn" path="res://Assets/Styles/CurrentlyActiveSetting.tres" id="1_13uuk"]
[ext_resource type="Script" uid="uid://me5dium4sdha" path="res://Scenes/Menus/general_settings.gd" id="1_bxju0"]
[ext_resource type="StyleBox" uid="uid://dtodajt4mqpah" path="res://Assets/Styles/CurrentlyInactiveSetting.tres" id="2_0xlck"]
[ext_resource type="StyleBox" uid="uid://qkajqm27c87c" path="res://Assets/Styles/CurrentlyInactiveSettingHoverPressed.tres" id="2_h7jkf"]
[ext_resource type="Texture2D" uid="uid://cnc8wrrgojiw8" path="res://Assets/Sprites/TempBack.png" id="4_h7jkf"]
[ext_resource type="Texture2D" uid="uid://byxx30nvmbm05" path="res://Assets/Sprites/TempBackHoverClicked.png" id="5_gjdnx"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_h7jkf"]
bg_color = Color(0.168627, 0.168627, 0.168627, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_gjdnx"]
bg_color = Color(0.168627, 0.168627, 0.168627, 1)
border_width_bottom = 5
border_color = Color(0.698039, 0.698039, 0.698039, 1)
border_blend = true

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_bxju0"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_litf7"]

[node name="GeneralSettings" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_bxju0")

[node name="Background" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_h7jkf")

[node name="TypeLine" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 10
anchor_right = 1.0
offset_bottom = 100.0
grow_horizontal = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_gjdnx")

[node name="SettingTypesContainer" type="HBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 10
anchor_right = 1.0
offset_bottom = 100.0
grow_horizontal = 2

[node name="GeneralButton" type="Button" parent="SettingTypesContainer"]
layout_mode = 2
size_flags_vertical = 8
theme_override_colors/font_hover_color = Color(1, 1, 1, 1)
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_colors/font_focus_color = Color(1, 1, 1, 1)
theme_override_colors/font_pressed_color = Color(1, 1, 1, 1)
theme_override_font_sizes/font_size = 32
theme_override_styles/focus = SubResource("StyleBoxEmpty_bxju0")
theme_override_styles/hover = ExtResource("1_13uuk")
theme_override_styles/pressed = ExtResource("1_13uuk")
theme_override_styles/normal = ExtResource("1_13uuk")
text = "General"

[node name="AudioButton" type="Button" parent="SettingTypesContainer"]
layout_mode = 2
size_flags_vertical = 8
theme_override_colors/font_hover_color = Color(1, 1, 1, 1)
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_colors/font_focus_color = Color(1, 1, 1, 1)
theme_override_colors/font_pressed_color = Color(1, 1, 1, 1)
theme_override_font_sizes/font_size = 32
theme_override_styles/focus = SubResource("StyleBoxEmpty_litf7")
theme_override_styles/hover = ExtResource("2_h7jkf")
theme_override_styles/pressed = ExtResource("2_h7jkf")
theme_override_styles/normal = ExtResource("2_0xlck")
text = "Audio"

[node name="VideoButton" type="Button" parent="SettingTypesContainer"]
layout_mode = 2
size_flags_vertical = 8
theme_override_colors/font_hover_color = Color(1, 1, 1, 1)
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_colors/font_focus_color = Color(1, 1, 1, 1)
theme_override_colors/font_pressed_color = Color(1, 1, 1, 1)
theme_override_font_sizes/font_size = 32
theme_override_styles/focus = SubResource("StyleBoxEmpty_litf7")
theme_override_styles/hover = ExtResource("2_h7jkf")
theme_override_styles/pressed = ExtResource("2_h7jkf")
theme_override_styles/normal = ExtResource("2_0xlck")
text = "Video
"

[node name="BackButton" type="TextureButton" parent="."]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -81.0
offset_top = 16.0
offset_right = -17.0
offset_bottom = 80.0
grow_horizontal = 0
texture_normal = ExtResource("4_h7jkf")
texture_pressed = ExtResource("5_gjdnx")
texture_hover = ExtResource("5_gjdnx")
stretch_mode = 4

[connection signal="pressed" from="BackButton" to="." method="_on_back_button_pressed"]
