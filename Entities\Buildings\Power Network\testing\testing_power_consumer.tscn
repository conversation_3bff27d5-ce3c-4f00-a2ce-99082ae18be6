[gd_scene load_steps=5 format=3 uid="uid://de6s4bwkw62cx"]

[ext_resource type="Script" uid="uid://baiuc1dqk224r" path="res://Entities/Buildings/Power Network/testing/testing_power_consumer.gd" id="1_cdgaa"]
[ext_resource type="Script" uid="uid://h5swltwpuw8s" path="res://Entities/Components/Power/power_buffer_component.gd" id="2_senlu"]
[ext_resource type="Script" uid="uid://dfrsc76sgl6kk" path="res://Entities/Components/Power/power_consumer_component.gd" id="3_q6uve"]
[ext_resource type="Script" uid="uid://2ud046riy2x2" path="res://Entities/Components/Debug/power_info_component.gd" id="4_2c3l8"]

[node name="TestingPowerConsumer" type="Node2D"]
script = ExtResource("1_cdgaa")

[node name="PowerBufferComponent" type="Node2D" parent="."]
script = ExtResource("2_senlu")
max_storage = 20.0
metadata/_custom_type_script = "uid://h5swltwpuw8s"

[node name="PowerReceiverComponent" type="Node2D" parent="." groups=["power_receiver"]]
script = ExtResource("3_q6uve")
metadata/_custom_type_script = "uid://dfrsc76sgl6kk"

[node name="PowerInfoComponent" type="Node2D" parent="."]
script = ExtResource("4_2c3l8")
metadata/_custom_type_script = "uid://2ud046riy2x2"
