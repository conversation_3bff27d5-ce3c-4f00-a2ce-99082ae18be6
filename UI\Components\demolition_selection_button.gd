@tool
class_name DemolitionSelectionBarButton
extends SelectionBar<PERSON>utton

@onready var EMPHASIS_LABEL_BOX_PREFAB: PackedScene = load("res://UI/Components/emphasis_label_box.tscn")

var info_label: EmphasisLabelBox

func _on_mouse_entered() -> void:
	info_label = EMPHASIS_LABEL_BOX_PREFAB.instantiate()
	info_label.label.text = "Deletion"
	$"/root/PlanetSurface/Camera2D/UI/HUD".add_child(info_label)
	info_label.position = global_position
	# This is a bit magical but it is also temporary
	info_label.position.y -= (info_label.get_rect().size.y / 2) + get_rect().size.y / 2
	info_label.position.x += info_label.get_rect().size.x / 4
	$Button.mouse_exited.connect(info_label._on_mouse_exited)
	info_label.show()


func _on_button_pressed() -> void:
	assert (stats != null)

	BuildingModeManager.selected_building_stats = stats

	StateManager.state = StateManager.States.STATE_DEMOLISH
	BuildingSignalBus.demolition_mode_triggered.emit()
