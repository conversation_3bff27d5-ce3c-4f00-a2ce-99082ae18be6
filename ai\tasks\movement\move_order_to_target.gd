@tool
extends BTAction

var npc: NPC

@export var target_var = &"target_pos"


func _generate_name() -> String:
	return "Set %s from move order" % target_var



func _setup() -> void:
	npc = agent as NPC



func _tick(_delta: float) -> Status:
	if npc.has_movement_orders():
		var next_order: Vector2 = npc.get_next_movement_order()
		blackboard.set_var(target_var, next_order)
		return SUCCESS
	return FAILURE
