@tool
extends BTAction

var npc: NPC

@export var target_var = &"target_pos"



func _generate_name() -> String:
	return "Move to " + target_var



func _setup() -> void:
	npc = agent as NPC



func _enter() -> void:
	npc.navigation.target_position = blackboard.get_var(target_var, Vector2.ZERO)



func _tick(_delta: float) -> Status:
	if npc.navigation.is_navigation_finished():
		return SUCCESS

	if not npc.navigation.is_target_reachable():
		return FAILURE

	npc.move()

	return RUNNING
