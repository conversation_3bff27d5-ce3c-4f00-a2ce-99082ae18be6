extends CharacterBody2D

@export var move_speed: float = 150.0

@onready var node_2d: Node2D = $Node2D


func _physics_process(_delta: float) -> void:
	var move_dir = Input.get_vector("move_left", "move_right", "move_up", "move_down");
	velocity = move_speed * move_dir
	move_and_slide()

	var facing_direction = global_position-get_global_mouse_position()
	if facing_direction.x:
		node_2d.scale.x = -1 if facing_direction.x > 0 else 1
