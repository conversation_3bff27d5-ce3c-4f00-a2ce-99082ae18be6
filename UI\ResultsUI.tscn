[gd_scene load_steps=12 format=3 uid="uid://cc34idqq1k5jk"]

[ext_resource type="StyleBox" uid="uid://8en3eownniur" path="res://Assets/Styles/GenericButtonHovered.tres" id="1_4wrxj"]
[ext_resource type="Script" uid="uid://b3ao3x3nhnt75" path="res://Scripts/results_ui.gd" id="1_gy020"]
[ext_resource type="StyleBox" uid="uid://br1jxnl5ox4pr" path="res://Assets/Styles/GenericButtonClicked.tres" id="2_gy020"]
[ext_resource type="StyleBox" uid="uid://byicfcubdmq4h" path="res://Assets/Styles/GenericButton.tres" id="3_gasjn"]
[ext_resource type="Texture2D" uid="uid://cq2or2tqvjdup" path="res://Assets/Sprites/Tick.svg" id="4_t1rnl"]
[ext_resource type="Texture2D" uid="uid://bf7st2emirxib" path="res://Assets/Sprites/Cross.svg" id="5_68wy8"]
[ext_resource type="Texture2D" uid="uid://bai8snsrpdyv2" path="res://Assets/Sprites/StarFull.svg" id="6_ffxp3"]
[ext_resource type="Texture2D" uid="uid://dl3ss5bkl65to" path="res://Assets/Sprites/StarEmpty.svg" id="7_4abi7"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_4x7og"]
bg_color = Color(0.160784, 0.160784, 0.160784, 0.45098)
border_width_left = 10
border_width_top = 10
border_width_right = 10
border_width_bottom = 10
border_color = Color(0.0117647, 0.0117647, 0.0117647, 0.482353)
corner_radius_top_left = 50
corner_radius_top_right = 50
corner_radius_bottom_right = 50
corner_radius_bottom_left = 50

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_w7ex4"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_n0i8w"]

[node name="ResultsUi" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_gy020")

[node name="Background" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -563.0
offset_top = -312.0
offset_right = 563.0
offset_bottom = 312.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_4x7og")

[node name="ContinueButton" type="Button" parent="Background"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = 32.0
offset_top = -119.0
offset_right = 514.0
offset_bottom = -40.0
grow_horizontal = 2
grow_vertical = 0
theme_override_font_sizes/font_size = 50
theme_override_styles/focus = SubResource("StyleBoxEmpty_w7ex4")
theme_override_styles/hover = ExtResource("1_4wrxj")
theme_override_styles/pressed = ExtResource("2_gy020")
theme_override_styles/normal = ExtResource("3_gasjn")
text = "Continue on planet"

[node name="BackButton" type="Button" parent="Background"]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -501.0
offset_top = -119.0
offset_right = -32.0
offset_bottom = -40.0
grow_horizontal = 2
grow_vertical = 0
theme_override_font_sizes/font_size = 50
theme_override_styles/focus = SubResource("StyleBoxEmpty_n0i8w")
theme_override_styles/hover = ExtResource("1_4wrxj")
theme_override_styles/pressed = ExtResource("2_gy020")
theme_override_styles/normal = ExtResource("3_gasjn")
text = "    Back to galaxy    "

[node name="GridContainer" type="GridContainer" parent="Background"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -317.0
offset_top = -33.0
offset_right = 317.0
offset_bottom = 181.0
grow_horizontal = 2
grow_vertical = 2
columns = 3

[node name="ResultTexture1" type="TextureRect" parent="Background/GridContainer"]
layout_mode = 2
texture = ExtResource("4_t1rnl")
expand_mode = 2
stretch_mode = 4

[node name="Condition1" type="Label" parent="Background/GridContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 30
text = "Generate X of X within 1 minute "

[node name="Status1" type="Label" parent="Background/GridContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 30
text = "(131/42)"

[node name="ResultTexture2" type="TextureRect" parent="Background/GridContainer"]
layout_mode = 2
texture = ExtResource("5_68wy8")
expand_mode = 2
stretch_mode = 4

[node name="Condition2" type="Label" parent="Background/GridContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 30
text = "Generate Y of Y within 1 minute "

[node name="Status2" type="Label" parent="Background/GridContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 30
text = "(0/13)"

[node name="ResultTexture3" type="TextureRect" parent="Background/GridContainer"]
layout_mode = 2
texture = ExtResource("5_68wy8")
expand_mode = 2
stretch_mode = 4

[node name="Condition3" type="Label" parent="Background/GridContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 30
text = "Don't use any Z"

[node name="Status3" type="Label" parent="Background/GridContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 30
text = "(Fail)"

[node name="Star1" type="TextureRect" parent="Background"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -240.0
offset_top = 118.0
offset_right = -98.0
offset_bottom = 260.0
grow_horizontal = 2
texture = ExtResource("6_ffxp3")
expand_mode = 2
stretch_mode = 4

[node name="Star2" type="TextureRect" parent="Background"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = 98.0
offset_top = 118.0
offset_right = 240.0
offset_bottom = 260.0
grow_horizontal = 2
texture = ExtResource("7_4abi7")
expand_mode = 2
stretch_mode = 4

[node name="Star3" type="TextureRect" parent="Background"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -119.0
offset_top = 22.0
offset_right = 119.0
offset_bottom = 260.0
grow_horizontal = 2
texture = ExtResource("7_4abi7")
expand_mode = 2
stretch_mode = 4

[connection signal="pressed" from="Background/ContinueButton" to="." method="_on_continue_button_pressed"]
[connection signal="pressed" from="Background/BackButton" to="." method="_on_back_button_pressed"]
