[gd_resource type="Resource" script_class="ItemHandlingBuildingStats" load_steps=5 format=3 uid="uid://bngmej0pvatss"]

[ext_resource type="Script" uid="uid://datmaph0rdfke" path="res://Entities/Buildings/item generator/item_generator.gd" id="1_1vifp"]
[ext_resource type="Script" uid="uid://qrdti4s4su0t" path="res://Entities/Buildings/building base/conveyor_based_building_stats.gd" id="2_qye6k"]
[ext_resource type="Texture2D" uid="uid://qet3baxifsjq" path="res://Assets/Sprites/32x32/SpriteSheets/Gen_sprite_sheet_anim.png" id="3_m2qtj"]

[sub_resource type="AtlasTexture" id="AtlasTexture_2r6bj"]
atlas = ExtResource("3_m2qtj")
region = Rect2(0, 0, 32, 32)

[resource]
resource_name = "Item Generator"
script = ExtResource("2_qye6k")
input_directions = 0
output_directions = 15
transport_speed = 0.0
translation_key = ""
dimensions = Vector2i(1, 1)
is_rotateable = false
building_type = 12
component_scripts = Array[Script]([])
cost = Dictionary[int, int]({})
texture = SubResource("AtlasTexture_2r6bj")
building_script = ExtResource("1_1vifp")
unlocked_by_default = false
menu_order_priority = 0
metadata/_custom_type_script = "uid://qrdti4s4su0t"
