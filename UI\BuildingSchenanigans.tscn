[gd_scene load_steps=8 format=3 uid="uid://dwc8lb5wdkgxl"]

[ext_resource type="PackedScene" uid="uid://dsyrj2krswx6n" path="res://Scenes/tiles.tscn" id="1_koumg"]
[ext_resource type="Script" uid="uid://d2s7r1nqci5th" path="res://Entities/NodeMap/space_camera.gd" id="2_3ufoy"]
[ext_resource type="Script" uid="uid://f7fqo8cjpp3s" path="res://Scripts/builder.gd" id="3_2sd1p"]
[ext_resource type="PackedScene" uid="uid://oaugs621o01i" path="res://UI/HUD.tscn" id="4_7ggjf"]
[ext_resource type="Script" uid="uid://cfad0l6rx7cdc" path="res://Scripts/building_ghost.gd" id="5_js4s1"]
[ext_resource type="Script" uid="uid://cgd2bow12o24m" path="res://Scripts/demolisher.gd" id="6_3lcyh"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_rqvqv"]
size = Vector2(111.833, 27.6063)

[node name="Schenanigans" type="Node2D"]

[node name="Tiles" parent="." instance=ExtResource("1_koumg")]

[node name="Camera2D" type="Camera2D" parent="."]
script = ExtResource("2_3ufoy")

[node name="Builder" type="StaticBody2D" parent="Camera2D"]
input_pickable = true
script = ExtResource("3_2sd1p")

[node name="CollisionShape2D" type="CollisionShape2D" parent="Camera2D/Builder"]
shape = SubResource("RectangleShape2D_rqvqv")

[node name="HUD" parent="Camera2D" instance=ExtResource("4_7ggjf")]
offset_top = 433.0
offset_bottom = 433.0
size_flags_horizontal = 6
size_flags_vertical = 6

[node name="BuildingGhost" type="Node2D" parent="Camera2D"]
script = ExtResource("5_js4s1")

[node name="Sprite2D" type="Sprite2D" parent="Camera2D/BuildingGhost"]

[node name="Demolisher" type="Node2D" parent="Camera2D"]
script = ExtResource("6_3lcyh")

[node name="Buildings" type="Node2D" parent="."]

[connection signal="building_mode_cancelled" from="Camera2D/Builder" to="Camera2D/BuildingGhost" method="_on_placement_grid_building_mode_cancelled"]
