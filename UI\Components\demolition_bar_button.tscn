[gd_scene load_steps=3 format=3 uid="uid://bhymfqe526qxr"]

[ext_resource type="Script" uid="uid://b5ao41iowasue" path="res://UI/Components/demolition_selection_button.gd" id="1_xu5u3"]
[ext_resource type="Texture2D" uid="uid://dw1xqrdodl3v1" path="res://Assets/Sprites/32x32/DeleteSmall.png" id="2_ndsha"]

[node name="DemolitonBarButton" type="PanelContainer"]
offset_right = 136.0
offset_bottom = 136.0
size_flags_horizontal = 0
size_flags_vertical = 0
script = ExtResource("1_xu5u3")

[node name="Building Description Container" type="MarginContainer" parent="."]
visible = false
layout_mode = 2

[node name="VBoxContainer" type="VBoxContainer" parent="Building Description Container"]
visible = false
layout_mode = 2

[node name="Building Name" type="Label" parent="Building Description Container/VBoxContainer"]
layout_mode = 2

[node name="Building Description" type="Label" parent="Building Description Container/VBoxContainer"]
layout_mode = 2

[node name="Button" type="Button" parent="."]
layout_mode = 2
focus_mode = 0
theme_override_constants/outline_size = 1
icon = ExtResource("2_ndsha")
icon_alignment = 1

[connection signal="mouse_entered" from="." to="." method="_on_mouse_entered"]
[connection signal="mouse_exited" from="." to="." method="_on_mouse_exited"]
[connection signal="mouse_exited" from="Button" to="." method="_on_button_mouse_exited"]
