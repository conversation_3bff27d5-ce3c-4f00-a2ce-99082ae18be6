[gd_scene load_steps=9 format=3 uid="uid://b5yvm84yul73k"]

[ext_resource type="Script" uid="uid://dumcarrwh4quv" path="res://Managers/audio_manager.gd" id="1_ewgv3"]
[ext_resource type="Script" uid="uid://bekm526o7tnrs" path="res://Assets/Sounds/SoundEffectSettings.gd" id="2_tg1pk"]
[ext_resource type="AudioStream" uid="uid://dmiwvsftdgtiv" path="res://Assets/Sounds/Building place.mp3" id="3_qjhlk"]
[ext_resource type="AudioStream" uid="uid://cw5fqsjw4yq6d" path="res://Assets/Sounds/Building destroy.mp3" id="4_phm2h"]
[ext_resource type="AudioStream" uid="uid://c6kk67e6bryf0" path="res://Assets/Sounds/UI Button.mp3" id="5_411yy"]

[sub_resource type="Resource" id="Resource_5rnmg"]
script = ExtResource("2_tg1pk")
limit = 5
type = 0
sound_effect = ExtResource("3_qjhlk")
volume = 0.0
pitch_scale = 1.0
pitch_randomness = 0.05
metadata/_custom_type_script = "uid://bekm526o7tnrs"

[sub_resource type="Resource" id="Resource_c72ha"]
script = ExtResource("2_tg1pk")
limit = 5
type = 1
sound_effect = ExtResource("4_phm2h")
volume = 0.0
pitch_scale = 1.0
pitch_randomness = 0.05
metadata/_custom_type_script = "uid://bekm526o7tnrs"

[sub_resource type="Resource" id="Resource_sywk8"]
script = ExtResource("2_tg1pk")
limit = 5
type = 2
sound_effect = ExtResource("5_411yy")
volume = 0.0
pitch_scale = 1.0
pitch_randomness = 0.05
metadata/_custom_type_script = "uid://bekm526o7tnrs"

[node name="AudioManager" type="Node2D"]
script = ExtResource("1_ewgv3")
sound_effects = Array[ExtResource("2_tg1pk")]([SubResource("Resource_5rnmg"), SubResource("Resource_c72ha"), SubResource("Resource_sywk8")])
