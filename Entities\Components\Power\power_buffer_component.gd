class_name PowerBufferComponent
extends PowerBaseComponent

## Total amount of power the buffer is able to hold
@export var max_storage := 10.0

## Actual amount of power currently in the buffer
var stored_power := 0.0: set = _set_stored_power

signal stored_energy_changed()

func _ready() -> void:
	var power_receiver_component: PowerReceiverComponent = get_parent().get_node_or_null("PowerReceiverComponent")
	if power_receiver_component:
		stored_energy_changed.connect(
			func():
				# Set receiver efficiency to 0 if already full, otherwise set it to a percentage
				# of power that it can still receive from its input capacity.
				power_receiver_component.efficiency = (
				0.0
				if stored_power >= max_storage
				else min((max_storage - stored_power) / power_receiver_component.power_amount, 1.0)
				)
		)
		power_receiver_component.power_received.connect(_on_power_received)
	
	var power_provider_component: PowerProviderComponent = get_parent().get_node_or_null("PowerProviderComponent")
	if power_provider_component:
		stored_energy_changed.connect(
			func():
				# Set efficiency to 0 if battery is empty, otherwise set it to a percentage
				# that it can provide from its amount and output capacity, up to 100%
				power_provider_component.efficiency = (0.0 if stored_power <= 0 else min(stored_power / power_provider_component.power_amount, 1.0))
		)
		power_provider_component.power_provided.connect(_on_power_provided)

func _set_stored_power(value: float) -> void:
	stored_power = max(0, min(value, max_storage))

	# Make sure all nodes are ready
	if not is_inside_tree():
		await self.ready

	stored_energy_changed.emit()

## Reduce the amount of power in the battery by the power value per second
func _on_power_provided(power: float, delta: float) -> void:
	self.stored_power -= power * delta

## Increase the amount of power in the battery by the power value per second
func _on_power_received(power: float, delta: float) -> void:
	self.stored_power += power * delta
