[gd_scene load_steps=3 format=3 uid="uid://ba58p81o8vfnq"]

[ext_resource type="Script" uid="uid://ddaxciimkuvq3" path="res://UI/Components/emphasis_label_box.gd" id="1_qhgms"]
[ext_resource type="PackedScene" uid="uid://fq6jqadjsv86" path="res://UI/Components/emphasis_label.tscn" id="2_nb0p5"]

[node name="Emphasis Label Box" type="PanelContainer" node_paths=PackedStringArray("label")]
offset_right = 156.0
offset_bottom = 33.0
size_flags_horizontal = 4
size_flags_vertical = 4
script = ExtResource("1_qhgms")
label = NodePath("MarginContainer/Building Name")

[node name="MarginContainer" type="MarginContainer" parent="."]
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 4
theme_override_constants/margin_left = 5
theme_override_constants/margin_top = 5
theme_override_constants/margin_right = 5
theme_override_constants/margin_bottom = 5

[node name="Building Name" parent="MarginContainer" instance=ExtResource("2_nb0p5")]
layout_mode = 2

[connection signal="mouse_exited" from="." to="." method="_on_mouse_exited"]
