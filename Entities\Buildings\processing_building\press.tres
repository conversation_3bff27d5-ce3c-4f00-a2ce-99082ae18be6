[gd_resource type="Resource" script_class="ItemHandlingBuildingStats" load_steps=8 format=3 uid="uid://c5pwi56ffyvyw"]

[ext_resource type="SpriteFrames" uid="uid://w1x4rhu07dgh" path="res://Assets/Sprites/Animations/32x32/Hammer.tres" id="1_0e8in"]
[ext_resource type="Script" uid="uid://b8bs15us03x8b" path="res://Entities/Buildings/processing_building/processing_building.gd" id="2_7d02k"]
[ext_resource type="Script" uid="uid://2ud046riy2x2" path="res://Entities/Components/Debug/power_info_component.gd" id="3_4ke32"]
[ext_resource type="Script" uid="uid://dfrsc76sgl6kk" path="res://Entities/Components/Power/power_consumer_component.gd" id="4_nfr1k"]
[ext_resource type="Script" uid="uid://qrdti4s4su0t" path="res://Entities/Buildings/building base/conveyor_based_building_stats.gd" id="5_d2027"]
[ext_resource type="Texture2D" uid="uid://c8ead5v6c41c" path="res://Assets/Sprites/32x32/SpriteSheets/Hammer_sprite_sheet_anim.png" id="6_p34uq"]

[sub_resource type="AtlasTexture" id="AtlasTexture_jcs2f"]
atlas = ExtResource("6_p34uq")
region = Rect2(0, 0, 32, 32)

[resource]
resource_name = "Press"
script = ExtResource("5_d2027")
input_directions = 2
output_directions = 1
transport_speed = 1.0
translation_key = ""
dimensions = Vector2i(1, 1)
is_rotateable = true
building_type = 9
component_scripts = Array[Script]([ExtResource("3_4ke32"), ExtResource("4_nfr1k")])
cost = Dictionary[int, int]({})
texture = SubResource("AtlasTexture_jcs2f")
animation_frames = ExtResource("1_0e8in")
building_script = ExtResource("2_7d02k")
unlocked_by_default = false
menu_order_priority = 0
metadata/_custom_type_script = "uid://qrdti4s4su0t"
