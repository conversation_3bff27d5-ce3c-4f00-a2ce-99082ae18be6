[gd_resource type="SpriteFrames" load_steps=38 format=3 uid="uid://cgl5i215q8mhb"]

[ext_resource type="Texture2D" uid="uid://bvpima88xy31e" path="res://Assets/Sprites/SpriteSheets/CornerNewSmall_sprite_sheet_anim.png" id="1_nmj3s"]

[sub_resource type="AtlasTexture" id="AtlasTexture_f283g"]
atlas = ExtResource("1_nmj3s")
region = Rect2(0, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_8db08"]
atlas = ExtResource("1_nmj3s")
region = Rect2(256, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_hjkdv"]
atlas = ExtResource("1_nmj3s")
region = Rect2(512, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_3lw5b"]
atlas = ExtResource("1_nmj3s")
region = Rect2(768, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_unr3j"]
atlas = ExtResource("1_nmj3s")
region = Rect2(1024, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_u08bf"]
atlas = ExtResource("1_nmj3s")
region = Rect2(1280, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_m1ueo"]
atlas = ExtResource("1_nmj3s")
region = Rect2(1536, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_2kkmf"]
atlas = ExtResource("1_nmj3s")
region = Rect2(1792, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_yc6r8"]
atlas = ExtResource("1_nmj3s")
region = Rect2(2048, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_mmafl"]
atlas = ExtResource("1_nmj3s")
region = Rect2(0, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_pvwfw"]
atlas = ExtResource("1_nmj3s")
region = Rect2(256, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_g4rw8"]
atlas = ExtResource("1_nmj3s")
region = Rect2(512, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_0rdfm"]
atlas = ExtResource("1_nmj3s")
region = Rect2(768, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_at0q0"]
atlas = ExtResource("1_nmj3s")
region = Rect2(1024, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_yhlmc"]
atlas = ExtResource("1_nmj3s")
region = Rect2(1280, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_5dm30"]
atlas = ExtResource("1_nmj3s")
region = Rect2(1536, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_svulq"]
atlas = ExtResource("1_nmj3s")
region = Rect2(1792, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_y3c3a"]
atlas = ExtResource("1_nmj3s")
region = Rect2(2048, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_u6nkr"]
atlas = ExtResource("1_nmj3s")
region = Rect2(0, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_poa56"]
atlas = ExtResource("1_nmj3s")
region = Rect2(256, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_5dyf7"]
atlas = ExtResource("1_nmj3s")
region = Rect2(512, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_2qabq"]
atlas = ExtResource("1_nmj3s")
region = Rect2(768, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_0sfui"]
atlas = ExtResource("1_nmj3s")
region = Rect2(1024, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_ky0vy"]
atlas = ExtResource("1_nmj3s")
region = Rect2(1280, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_41d0h"]
atlas = ExtResource("1_nmj3s")
region = Rect2(1536, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_a8mkh"]
atlas = ExtResource("1_nmj3s")
region = Rect2(1792, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_pnxmi"]
atlas = ExtResource("1_nmj3s")
region = Rect2(2048, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_oph4u"]
atlas = ExtResource("1_nmj3s")
region = Rect2(0, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_pinbu"]
atlas = ExtResource("1_nmj3s")
region = Rect2(256, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_axcv5"]
atlas = ExtResource("1_nmj3s")
region = Rect2(512, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_clxeo"]
atlas = ExtResource("1_nmj3s")
region = Rect2(768, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_jq0dn"]
atlas = ExtResource("1_nmj3s")
region = Rect2(1024, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_gq023"]
atlas = ExtResource("1_nmj3s")
region = Rect2(1280, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_l5spi"]
atlas = ExtResource("1_nmj3s")
region = Rect2(1536, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_opf3v"]
atlas = ExtResource("1_nmj3s")
region = Rect2(1792, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_jk1k3"]
atlas = ExtResource("1_nmj3s")
region = Rect2(2048, 256, 256, 256)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_f283g")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8db08")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hjkdv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3lw5b")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_unr3j")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_u08bf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_m1ueo")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_2kkmf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_yc6r8")
}],
"loop": true,
"name": &"Down",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_mmafl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_pvwfw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_g4rw8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_0rdfm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_at0q0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_yhlmc")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5dm30")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_svulq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_y3c3a")
}],
"loop": true,
"name": &"Left",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_u6nkr")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_poa56")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5dyf7")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_2qabq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_0sfui")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ky0vy")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_41d0h")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_a8mkh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_pnxmi")
}],
"loop": true,
"name": &"Right",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_oph4u")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_pinbu")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_axcv5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_clxeo")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_jq0dn")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gq023")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_l5spi")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_opf3v")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_jk1k3")
}],
"loop": true,
"name": &"Up",
"speed": 10.0
}]
