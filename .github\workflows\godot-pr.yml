name: "godot-pr"
on:
  pull_request:
    types: [review_requested, ready_for_review]

env:
  GODOT_VERSION: 4.4.1
  EXPORT_NAME: ReFactory
  PROJECT_PATH: .

jobs:
  export-windows:
    name: Windows Build
    runs-on: ubuntu-22.04  # Use 22.04 with godot 4
    container:
      image: barichello/godot-ci:4.4.1
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          lfs: true
      - name: Setup
        run: |
          mkdir -v -p ~/.local/share/godot/export_templates/
          mkdir -v -p ~/.config/
          mv /root/.config/godot ~/.config/godot
          mv /root/.local/share/godot/export_templates/${GODOT_VERSION}.stable ~/.local/share/godot/export_templates/${GODOT_VERSION}.stable
      - name: Windows Build
        run: |
          mkdir -v -p build/windows
          EXPORT_DIR="$(readlink -f build)"
          cd $PROJECT_PATH
          godot --headless --verbose --export-release "windows" "$EXPORT_DIR/windows/$EXPORT_NAME.exe"
      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: windows
          path: build/windows