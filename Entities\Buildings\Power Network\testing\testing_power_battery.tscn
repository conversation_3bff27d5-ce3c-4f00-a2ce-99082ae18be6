[gd_scene load_steps=5 format=3 uid="uid://c5hbk2wlqmp0g"]

[ext_resource type="Script" uid="uid://h5swltwpuw8s" path="res://Entities/Components/Power/power_buffer_component.gd" id="2_o3kjs"]
[ext_resource type="Script" uid="uid://cxc5br52a2krl" path="res://Entities/Components/Power/power_provider_component.gd" id="3_8sswq"]
[ext_resource type="Script" uid="uid://dfrsc76sgl6kk" path="res://Entities/Components/Power/power_consumer_component.gd" id="4_50a01"]
[ext_resource type="Script" uid="uid://2ud046riy2x2" path="res://Entities/Components/Debug/power_info_component.gd" id="5_runih"]

[node name="TestingPowerBattery" type="Node2D"]

[node name="PowerBufferComponent" type="Node2D" parent="."]
script = ExtResource("2_o3kjs")
max_storage = 100.0
metadata/_custom_type_script = "uid://h5swltwpuw8s"

[node name="PowerProviderComponent" type="Node2D" parent="." groups=["power_provider"]]
script = ExtResource("3_8sswq")
power_amount = 20.0
power_priority = 1
metadata/_custom_type_script = "uid://cxc5br52a2krl"

[node name="PowerReceiverComponent" type="Node2D" parent="." groups=["power_receiver"]]
script = ExtResource("4_50a01")
power_priority = 1
metadata/_custom_type_script = "uid://dfrsc76sgl6kk"

[node name="PowerInfoComponent" type="Node2D" parent="."]
script = ExtResource("5_runih")
metadata/_custom_type_script = "uid://2ud046riy2x2"
