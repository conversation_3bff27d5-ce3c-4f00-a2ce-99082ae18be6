[gd_scene load_steps=13 format=3 uid="uid://d3e3ovy5ey6ww"]

[ext_resource type="SpriteFrames" uid="uid://deq1e81m0rmy" path="res://Assets/Sprites/Animations/HighPolyPlanetAnimation.tres" id="1_f578k"]
[ext_resource type="Script" uid="uid://bkweay1fdq6a8" path="res://Scripts/run_animation_on_start.gd" id="2_rftdn"]
[ext_resource type="StyleBox" uid="uid://8en3eownniur" path="res://Assets/Styles/GenericButtonHovered.tres" id="3_dxqk6"]
[ext_resource type="StyleBox" uid="uid://br1jxnl5ox4pr" path="res://Assets/Styles/GenericButtonClicked.tres" id="4_pdsm0"]
[ext_resource type="StyleBox" uid="uid://byicfcubdmq4h" path="res://Assets/Styles/GenericButton.tres" id="5_i87a8"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_b142r"]
bg_color = Color(0.184314, 0.184314, 0.184314, 1)
corner_radius_top_left = 50
corner_radius_top_right = 50
corner_radius_bottom_right = 50
corner_radius_bottom_left = 50

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_f578k"]
bg_color = Color(0.0646965, 0.0646965, 0.0646965, 1)
corner_radius_top_left = 150
corner_radius_top_right = 150
corner_radius_bottom_right = 150
corner_radius_bottom_left = 150

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_5trh3"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_1atef"]

[sub_resource type="Animation" id="Animation_dxqk6"]
resource_name = "ShowMenu"
length = 0.3
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.3),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(0.1, 0.1), Vector2(1, 1)]
}

[sub_resource type="Animation" id="Animation_pdsm0"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.1, 0.1)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_i87a8"]
_data = {
&"RESET": SubResource("Animation_pdsm0"),
&"ShowMenu": SubResource("Animation_dxqk6")
}

[node name="SelectedPlanetMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
scale = Vector2(0.1, 0.1)

[node name="Background" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -320.0
offset_top = -180.0
offset_right = 320.0
offset_bottom = 180.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_b142r")

[node name="PlanetBackground" type="Panel" parent="Background"]
layout_mode = 0
offset_left = 12.0
offset_top = 30.0
offset_right = 312.0
offset_bottom = 330.0
pivot_offset = Vector2(150, 150)
theme_override_styles/panel = SubResource("StyleBoxFlat_f578k")

[node name="PlanetSprite" type="AnimatedSprite2D" parent="Background"]
position = Vector2(162, 180)
sprite_frames = ExtResource("1_f578k")
script = ExtResource("2_rftdn")

[node name="PlanetInfo" type="VBoxContainer" parent="Background"]
clip_contents = true
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_top = -180.0
offset_right = 320.0
offset_bottom = 180.0
grow_horizontal = 2
grow_vertical = 2

[node name="PlanetName" type="Label" parent="Background/PlanetInfo"]
layout_mode = 2
theme_override_font_sizes/font_size = 30
text = "Brmbl H-35"

[node name="Divider" type="Panel" parent="Background/PlanetInfo"]
custom_minimum_size = Vector2(200, 3)
layout_mode = 2
size_flags_horizontal = 0
size_flags_vertical = 4
theme_override_styles/panel = SubResource("StyleBoxFlat_5trh3")

[node name="Objective1" type="HBoxContainer" parent="Background/PlanetInfo"]
layout_mode = 2

[node name="Task1" type="Label" parent="Background/PlanetInfo/Objective1"]
custom_minimum_size = Vector2(200, 0)
layout_mode = 2
theme_override_colors/font_color = Color(0, 1, 0, 1)
theme_override_font_sizes/font_size = 20
text = "Generate X of X within 1 minute"
autowrap_mode = 2

[node name="Status1" type="Label" parent="Background/PlanetInfo/Objective1"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
theme_override_colors/font_color = Color(0, 1, 0, 1)
theme_override_font_sizes/font_size = 20
text = "(131/42)"
autowrap_mode = 2

[node name="Objective2" type="HBoxContainer" parent="Background/PlanetInfo"]
layout_mode = 2

[node name="Task2" type="Label" parent="Background/PlanetInfo/Objective2"]
custom_minimum_size = Vector2(200, 0)
layout_mode = 2
theme_override_colors/font_color = Color(1, 0, 0, 1)
theme_override_font_sizes/font_size = 20
text = "Generate Y of Y within 1 minute"
autowrap_mode = 2

[node name="Status2" type="Label" parent="Background/PlanetInfo/Objective2"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
theme_override_colors/font_color = Color(1, 0, 0, 1)
theme_override_font_sizes/font_size = 20
text = "(0/13)"
autowrap_mode = 2

[node name="Objective3" type="HBoxContainer" parent="Background/PlanetInfo"]
layout_mode = 2

[node name="Task3" type="Label" parent="Background/PlanetInfo/Objective3"]
custom_minimum_size = Vector2(200, 0)
layout_mode = 2
theme_override_colors/font_color = Color(1, 0, 0, 1)
theme_override_font_sizes/font_size = 20
text = "Don't use any Z"
autowrap_mode = 2

[node name="Status3" type="Label" parent="Background/PlanetInfo/Objective3"]
custom_minimum_size = Vector2(100, 0)
layout_mode = 2
theme_override_colors/font_color = Color(1, 0, 0, 1)
theme_override_font_sizes/font_size = 20
text = "(Fail)"
autowrap_mode = 2

[node name="GoToPlanetButton" type="Button" parent="Background/PlanetInfo"]
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 6
theme_override_font_sizes/font_size = 30
theme_override_styles/focus = SubResource("StyleBoxEmpty_1atef")
theme_override_styles/hover = ExtResource("3_dxqk6")
theme_override_styles/pressed = ExtResource("4_pdsm0")
theme_override_styles/normal = ExtResource("5_i87a8")
text = "Go to planet"

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_i87a8")
}
