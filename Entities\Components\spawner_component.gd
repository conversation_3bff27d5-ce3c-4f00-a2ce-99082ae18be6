extends Node2D


@export var unit: PackedScene
@export var SpawnDelay: float = 10000
@export var MaxUnitCount: int = 5
@export var SpawnOnInit: bool = true
signal unit_spawned(unit: Node)
var LastSpawnTime: float
var spawned_units: Array = []


func _init() -> void:
	LastSpawnTime = Time.get_ticks_msec()
	if SpawnOnInit:
		LastSpawnTime -= SpawnDelay



func _process(_delta: float) -> void:
	# Clean up the list: remove units that are no longer in the scene
	spawned_units = spawned_units.filter(func(u): return is_instance_valid(u))

	if Time.get_ticks_msec() - LastSpawnTime > SpawnDelay:
		if spawned_units.size() < MaxUnitCount:
			spawn_unit()
		LastSpawnTime = Time.get_ticks_msec()



func spawn_unit() -> void:
	if unit:
		var unit_instance = unit.instantiate()

		unit_instance.position = global_position + Vector2(randf_range(-50, 50), randf_range(-50, 50))
		get_tree().current_scene.add_child(unit_instance)
		spawned_units.append(unit_instance)

		unit_spawned.emit(unit_instance)
