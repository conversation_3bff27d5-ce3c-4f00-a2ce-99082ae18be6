[gd_scene load_steps=9 format=3 uid="uid://cdf1fbdtcu3dl"]

[ext_resource type="Script" uid="uid://b53ae4jon06ay" path="res://Entities/Player/player_controller.gd" id="1_kpjcp"]
[ext_resource type="Texture2D" uid="uid://jr0ng3gp6gx2" path="res://Assets/IconGodotNode/node/icon_character.png" id="1_xhfnw"]
[ext_resource type="PackedScene" uid="uid://cvogo0j7ferfa" path="res://Entities/Components/health_component.tscn" id="3_an6q6"]
[ext_resource type="PackedScene" uid="uid://dhvj5lqfiuttb" path="res://Entities/Components/hit_box_component.tscn" id="3_vweq0"]
[ext_resource type="PackedScene" uid="uid://sgsrhtv7bywd" path="res://Entities/Components/team_component.tscn" id="4_1des2"]
[ext_resource type="PackedScene" uid="uid://b7xm2m6j41ym4" path="res://Entities/Components/Equipment/Gun.tscn" id="6_t38ms"]

[sub_resource type="CircleShape2D" id="CircleShape2D_gqquq"]

[sub_resource type="CircleShape2D" id="CircleShape2D_vweq0"]

[node name="Player" type="CharacterBody2D" groups=["Entity"]]
motion_mode = 1
wall_min_slide_angle = 0.0
script = ExtResource("1_kpjcp")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_gqquq")

[node name="Node2D" type="Node2D" parent="."]

[node name="Sprite2D" type="Sprite2D" parent="Node2D"]
texture = ExtResource("1_xhfnw")

[node name="Gun" parent="Node2D" instance=ExtResource("6_t38ms")]
position = Vector2(6, 7)

[node name="Camera2D" type="Camera2D" parent="."]
zoom = Vector2(5, 5)

[node name="HitBoxComponent" parent="." instance=ExtResource("3_vweq0")]

[node name="CollisionShape2D" type="CollisionShape2D" parent="HitBoxComponent"]
shape = SubResource("CircleShape2D_vweq0")

[node name="HealthComponent" parent="." instance=ExtResource("3_an6q6")]
position = Vector2(0, -12)

[node name="TeamComponent" parent="." instance=ExtResource("4_1des2")]
team = 1
