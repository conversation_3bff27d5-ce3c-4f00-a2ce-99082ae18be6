extends Node


# TODO later change this to something that can hold all three conditions
var condition: Callable
var condition_triggered: bool = false

# Data used for default condition
var items_recieved: int = 100

# Default condition, used so that no null pointers happen
func decrease_and_check(item_consumed: ItemType.Enum, throughput: int = 0, sink_id: int = 0) -> bool:
	if items_recieved < 1 and item_consumed != null:
		return true
	items_recieved -= 1
	return false

# Data used for throughput condition
var target_throughput: int = 50
var sink_throughputs: Dictionary[int, int]

# TODO This check has the 10 sec problem where false postives can be created and does not divide 
# throughputs of different items
func is_throughput_high_enough(item_consumed: ItemType.Enum, throughput: int = 0, sink_id: int = 0) -> bool:
	sink_throughputs[sink_id] = throughput
	var sum = 0
	for each in sink_throughputs.values():
		sum += each
	return sum > target_throughput


func _ready() -> void:
	condition = self.is_throughput_high_enough
	BuildingSignalBus.sink_consumed.connect(_check_condition)


func _input(event: InputEvent) -> void:
	if event.is_action_pressed(&"instant_win"):
		BuildingSignalBus.player_won.emit()
	if event.is_action_pressed("camera_move_up"):
		var sum = 0
		for each in sink_throughputs.values():
			sum += each
		print(sum)


func _check_condition(item_consumed: ItemType.Enum, throughput: int, sink_id: int) -> void:
	if condition.call(item_consumed, throughput, sink_id) and not condition_triggered:
		condition_triggered = true
		BuildingSignalBus.player_won.emit()
