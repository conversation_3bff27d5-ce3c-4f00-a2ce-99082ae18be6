[gd_resource type="SpriteFrames" load_steps=38 format=3 uid="uid://djli40r0dfhfm"]

[ext_resource type="Texture2D" uid="uid://cwrut06y4ay04" path="res://Assets/Sprites/SpriteSheets/StraightNewSmall_sprite_sheet_anim.png" id="1_ls2vt"]

[sub_resource type="AtlasTexture" id="AtlasTexture_xnbon"]
atlas = ExtResource("1_ls2vt")
region = Rect2(0, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_a8pof"]
atlas = ExtResource("1_ls2vt")
region = Rect2(256, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_l4sgf"]
atlas = ExtResource("1_ls2vt")
region = Rect2(512, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_6humx"]
atlas = ExtResource("1_ls2vt")
region = Rect2(768, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_51fyd"]
atlas = ExtResource("1_ls2vt")
region = Rect2(1024, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_8eltp"]
atlas = ExtResource("1_ls2vt")
region = Rect2(1280, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_3mfg5"]
atlas = ExtResource("1_ls2vt")
region = Rect2(1536, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_q6rpp"]
atlas = ExtResource("1_ls2vt")
region = Rect2(1792, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_75j0r"]
atlas = ExtResource("1_ls2vt")
region = Rect2(2048, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_6i8uc"]
atlas = ExtResource("1_ls2vt")
region = Rect2(0, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_baap3"]
atlas = ExtResource("1_ls2vt")
region = Rect2(256, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_ycb78"]
atlas = ExtResource("1_ls2vt")
region = Rect2(512, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_5qb0x"]
atlas = ExtResource("1_ls2vt")
region = Rect2(768, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_4421a"]
atlas = ExtResource("1_ls2vt")
region = Rect2(1024, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_lqccr"]
atlas = ExtResource("1_ls2vt")
region = Rect2(1280, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_6uy65"]
atlas = ExtResource("1_ls2vt")
region = Rect2(1536, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_0tij1"]
atlas = ExtResource("1_ls2vt")
region = Rect2(1792, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_7uhrj"]
atlas = ExtResource("1_ls2vt")
region = Rect2(2048, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_eadlp"]
atlas = ExtResource("1_ls2vt")
region = Rect2(0, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_8wuym"]
atlas = ExtResource("1_ls2vt")
region = Rect2(256, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_8c3yx"]
atlas = ExtResource("1_ls2vt")
region = Rect2(512, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_q1dcw"]
atlas = ExtResource("1_ls2vt")
region = Rect2(768, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_3cghm"]
atlas = ExtResource("1_ls2vt")
region = Rect2(1024, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_jpjpb"]
atlas = ExtResource("1_ls2vt")
region = Rect2(1280, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_tj1a3"]
atlas = ExtResource("1_ls2vt")
region = Rect2(1536, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_01m7w"]
atlas = ExtResource("1_ls2vt")
region = Rect2(1792, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_rywwu"]
atlas = ExtResource("1_ls2vt")
region = Rect2(2048, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_bf64a"]
atlas = ExtResource("1_ls2vt")
region = Rect2(0, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_wpoo2"]
atlas = ExtResource("1_ls2vt")
region = Rect2(256, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_jdsss"]
atlas = ExtResource("1_ls2vt")
region = Rect2(512, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_i4wuf"]
atlas = ExtResource("1_ls2vt")
region = Rect2(768, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_fb1b1"]
atlas = ExtResource("1_ls2vt")
region = Rect2(1024, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_a2sjp"]
atlas = ExtResource("1_ls2vt")
region = Rect2(1280, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_18c8l"]
atlas = ExtResource("1_ls2vt")
region = Rect2(1536, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_g5ic8"]
atlas = ExtResource("1_ls2vt")
region = Rect2(1792, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_aep42"]
atlas = ExtResource("1_ls2vt")
region = Rect2(2048, 256, 256, 256)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_xnbon")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_a8pof")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_l4sgf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6humx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_51fyd")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8eltp")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3mfg5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_q6rpp")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_75j0r")
}],
"loop": true,
"name": &"Down",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_6i8uc")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_baap3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ycb78")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5qb0x")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4421a")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_lqccr")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6uy65")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_0tij1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7uhrj")
}],
"loop": true,
"name": &"Left",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_eadlp")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8wuym")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8c3yx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_q1dcw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3cghm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_jpjpb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tj1a3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_01m7w")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rywwu")
}],
"loop": true,
"name": &"Right",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_bf64a")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wpoo2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_jdsss")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_i4wuf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_fb1b1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_a2sjp")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_18c8l")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_g5ic8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_aep42")
}],
"loop": true,
"name": &"Up",
"speed": 5.0
}]
