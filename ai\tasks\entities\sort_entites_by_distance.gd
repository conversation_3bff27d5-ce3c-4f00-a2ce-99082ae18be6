@tool
extends BTAction

@export var entities_var := &"entities"


func _generate_name() -> String:
	return "Sorts %s by distance" % entities_var



func _tick(_delta: float) -> Status:
	var entities: Array = blackboard.get_var(entities_var, null)
	if entities == null or entities.is_empty():
		return FAILURE

	var owner_pos = agent.global_position

	# Precompute distances once
	var entity_dist_pairs := []
	for entity in entities:
		entity_dist_pairs.append([owner_pos.distance_squared_to(entity.global_position), entity])

	# Sort by distance (ascending)
	entity_dist_pairs.sort()

	# Extract sorted entities
	var sorted_entities: Array[Node2D] = []
	for pair in entity_dist_pairs:
		sorted_entities.append(pair[1])

	blackboard.set_var(entities_var, sorted_entities)
	return SUCCESS
