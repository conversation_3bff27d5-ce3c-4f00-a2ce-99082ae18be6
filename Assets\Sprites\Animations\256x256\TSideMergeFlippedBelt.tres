[gd_resource type="SpriteFrames" load_steps=38 format=3 uid="uid://dbqpb7adiirpc"]

[ext_resource type="Texture2D" uid="uid://b5bn76g5a8dbw" path="res://Assets/Sprites/SpriteSheets/ThreeSideMergeNewFlippedSmall_sprite_sheet_anim.png" id="1_8inpp"]

[sub_resource type="AtlasTexture" id="AtlasTexture_kh78p"]
atlas = ExtResource("1_8inpp")
region = Rect2(0, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_upndh"]
atlas = ExtResource("1_8inpp")
region = Rect2(256, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_wl5pe"]
atlas = ExtResource("1_8inpp")
region = Rect2(512, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_kngw8"]
atlas = ExtResource("1_8inpp")
region = Rect2(768, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_451r8"]
atlas = ExtResource("1_8inpp")
region = Rect2(1024, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_rr78k"]
atlas = ExtResource("1_8inpp")
region = Rect2(1280, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_tkd74"]
atlas = ExtResource("1_8inpp")
region = Rect2(1536, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_p3dna"]
atlas = ExtResource("1_8inpp")
region = Rect2(1792, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_4omkj"]
atlas = ExtResource("1_8inpp")
region = Rect2(2048, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_ogg4k"]
atlas = ExtResource("1_8inpp")
region = Rect2(0, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_fcg38"]
atlas = ExtResource("1_8inpp")
region = Rect2(256, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_73cwp"]
atlas = ExtResource("1_8inpp")
region = Rect2(512, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_h6edk"]
atlas = ExtResource("1_8inpp")
region = Rect2(768, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_p08sl"]
atlas = ExtResource("1_8inpp")
region = Rect2(1024, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_j2a0d"]
atlas = ExtResource("1_8inpp")
region = Rect2(1280, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_np6iq"]
atlas = ExtResource("1_8inpp")
region = Rect2(1536, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_c6kfp"]
atlas = ExtResource("1_8inpp")
region = Rect2(1792, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_bki1d"]
atlas = ExtResource("1_8inpp")
region = Rect2(2048, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_b3vip"]
atlas = ExtResource("1_8inpp")
region = Rect2(0, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_yv1cg"]
atlas = ExtResource("1_8inpp")
region = Rect2(256, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_cvaav"]
atlas = ExtResource("1_8inpp")
region = Rect2(512, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_yfhup"]
atlas = ExtResource("1_8inpp")
region = Rect2(768, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_je34v"]
atlas = ExtResource("1_8inpp")
region = Rect2(1024, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_8jhvg"]
atlas = ExtResource("1_8inpp")
region = Rect2(1280, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_s36fq"]
atlas = ExtResource("1_8inpp")
region = Rect2(1536, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_kkqw1"]
atlas = ExtResource("1_8inpp")
region = Rect2(1792, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_vcod2"]
atlas = ExtResource("1_8inpp")
region = Rect2(2048, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_4ok4j"]
atlas = ExtResource("1_8inpp")
region = Rect2(0, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_mlilp"]
atlas = ExtResource("1_8inpp")
region = Rect2(256, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_obsfh"]
atlas = ExtResource("1_8inpp")
region = Rect2(512, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_4nyjl"]
atlas = ExtResource("1_8inpp")
region = Rect2(768, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_3mqjc"]
atlas = ExtResource("1_8inpp")
region = Rect2(1024, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_a3qu5"]
atlas = ExtResource("1_8inpp")
region = Rect2(1280, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_qgicg"]
atlas = ExtResource("1_8inpp")
region = Rect2(1536, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_7o0ce"]
atlas = ExtResource("1_8inpp")
region = Rect2(1792, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_ah0of"]
atlas = ExtResource("1_8inpp")
region = Rect2(2048, 256, 256, 256)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_kh78p")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_upndh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wl5pe")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kngw8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_451r8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rr78k")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tkd74")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_p3dna")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4omkj")
}],
"loop": true,
"name": &"Down",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_ogg4k")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_fcg38")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_73cwp")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_h6edk")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_p08sl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_j2a0d")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_np6iq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_c6kfp")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bki1d")
}],
"loop": true,
"name": &"Left",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_b3vip")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_yv1cg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_cvaav")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_yfhup")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_je34v")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8jhvg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_s36fq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kkqw1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vcod2")
}],
"loop": true,
"name": &"Right",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_4ok4j")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mlilp")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_obsfh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4nyjl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3mqjc")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_a3qu5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qgicg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7o0ce")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ah0of")
}],
"loop": true,
"name": &"Up",
"speed": 10.0
}]
