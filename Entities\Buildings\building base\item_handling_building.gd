## Represents any building that is capable of holding some items.
class_name ItemHandlingBuilding
extends Building

@onready var ITEM_TEMPLATE = load("res://Entities/Items/Item.tscn")

@onready var buildings_node: Node2D = get_tree().get_current_scene().get_node("Buildings")

## Emitted when the building is trying to push an item to one of its outputs
signal push_item(item: Item)

## Emitted when an item was successfully received
@warning_ignore("unused_signal")
signal item_received(item: Item, building: Building)

## Emiteed when we want to confirm that the item should be transporter
@warning_ignore("unused_signal")
signal item_transport_confirmed(item: Item, building: Building)

## Emitted when the building has received an item
@warning_ignore("unused_signal")
signal has_item

## Emitted if the building is empty
@warning_ignore("unused_signal")
signal is_empty


@export_category("Instance based Runtime")
## List of buildings that serve as an input to this building
@export var input_buildings: Array[Building]
## List of buildings that serve as an output to this building
@export var output_buildings: Array[Building]


## Get the set of currently held items that are dedicated as outputs.
func get_output_items() -> Array[Item]:
	return []


## Method that is executed after the [signal ItemHandlingBuilding.push_item] signal is fired.[br]
## It is supposed to be implemented by child classes.
func _after_push_item(_item: Item) -> void:
	pass


## Method that is executed after a single iteration of process method.[br]
## It is supposed to be implemented by child classes.[br]
## Thanks to this it should be possible to rely on the logic of this class
## without implementation of own process method.
func _after_process() -> void:
	pass

## Method that is executed after a single iteration of process method.[br]
## It is supposed to be implemented by child classes.[br]
func _before_process() -> void:
	pass


## Handling of the [signal ItemHandlingBuilding.push_item] signal.[br]
## It has to be declared so that it can be connected by other methods. However, after
## receiving the desired signal the runtime calls the overriden method of a child class.
func _on_push_item(_item: Item) -> void:
	pass


## Handling of the [signal ItemHandlingBuilding.item_received] signal.[br]
## It has to be declared so that it can be connected by other methods. However, after
## receiving the desired signal the runtime calls the overriden method of a child class.
func _on_item_received(_item: Item, _building: Building) -> void:
	pass


func _on_item_transport_confirm(_item: Item, _building: Building) -> void:
	pass


func _instantiate_item(item_stats: ItemData) -> Item:
	var item: Item = ITEM_TEMPLATE.instantiate()
	item.item_data = item_stats
	add_child(item)
	item.owner = buildings_node

	# we have to use the global position because otherwise the item has its position relative to
	# the parent (the generator) and is offseted by the distance to the world origin.
	item.global_position = global_position
	# This way the sprite is ignoring the rotation of parent
	item.global_rotation = 0.0
	return item


# This implementation of process method shouldn't be overriden by child classes if it is not really
# needed! Rather override the _after_* methods that have defined position in this method.
func _process(_delta: float) -> void:
	if Engine.is_editor_hint():
		return

	_before_process()

	for item: Item in get_output_items():
		if item == null or item.is_moving():
			continue
		push_item.emit(item)
		_after_push_item(item)

	_after_process()
