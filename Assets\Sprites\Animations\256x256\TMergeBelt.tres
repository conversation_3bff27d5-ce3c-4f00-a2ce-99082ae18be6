[gd_resource type="SpriteFrames" load_steps=38 format=3 uid="uid://fk8vimo5okrr"]

[ext_resource type="Texture2D" uid="uid://bdvtm58t2aw7w" path="res://Assets/Sprites/SpriteSheets/ThreeMergeNewSmall_sprite_sheet_anim.png" id="1_u20od"]

[sub_resource type="AtlasTexture" id="AtlasTexture_ktjth"]
atlas = ExtResource("1_u20od")
region = Rect2(0, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_eny82"]
atlas = ExtResource("1_u20od")
region = Rect2(256, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_i47jj"]
atlas = ExtResource("1_u20od")
region = Rect2(512, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_rxqsm"]
atlas = ExtResource("1_u20od")
region = Rect2(768, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_sfosw"]
atlas = ExtResource("1_u20od")
region = Rect2(1024, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_cfdbf"]
atlas = ExtResource("1_u20od")
region = Rect2(1280, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_r4yvk"]
atlas = ExtResource("1_u20od")
region = Rect2(1536, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_gt80w"]
atlas = ExtResource("1_u20od")
region = Rect2(1792, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_hfod8"]
atlas = ExtResource("1_u20od")
region = Rect2(2048, 768, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_2yvwf"]
atlas = ExtResource("1_u20od")
region = Rect2(0, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_crk0q"]
atlas = ExtResource("1_u20od")
region = Rect2(256, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_km2du"]
atlas = ExtResource("1_u20od")
region = Rect2(512, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_cj320"]
atlas = ExtResource("1_u20od")
region = Rect2(768, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_kgbb8"]
atlas = ExtResource("1_u20od")
region = Rect2(1024, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_vj2qg"]
atlas = ExtResource("1_u20od")
region = Rect2(1280, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_1knyk"]
atlas = ExtResource("1_u20od")
region = Rect2(1536, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_mf5cg"]
atlas = ExtResource("1_u20od")
region = Rect2(1792, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_oav0o"]
atlas = ExtResource("1_u20od")
region = Rect2(2048, 512, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_1irqt"]
atlas = ExtResource("1_u20od")
region = Rect2(0, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_mvs6t"]
atlas = ExtResource("1_u20od")
region = Rect2(256, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_au1uo"]
atlas = ExtResource("1_u20od")
region = Rect2(512, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_ap213"]
atlas = ExtResource("1_u20od")
region = Rect2(768, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_q1f5x"]
atlas = ExtResource("1_u20od")
region = Rect2(1024, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_gvgq1"]
atlas = ExtResource("1_u20od")
region = Rect2(1280, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_rhlgc"]
atlas = ExtResource("1_u20od")
region = Rect2(1536, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_3rvm4"]
atlas = ExtResource("1_u20od")
region = Rect2(1792, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_vcbee"]
atlas = ExtResource("1_u20od")
region = Rect2(2048, 0, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_hbpp6"]
atlas = ExtResource("1_u20od")
region = Rect2(0, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_kacpb"]
atlas = ExtResource("1_u20od")
region = Rect2(256, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_x0og6"]
atlas = ExtResource("1_u20od")
region = Rect2(512, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_btfxi"]
atlas = ExtResource("1_u20od")
region = Rect2(768, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_6gue7"]
atlas = ExtResource("1_u20od")
region = Rect2(1024, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_ddkyj"]
atlas = ExtResource("1_u20od")
region = Rect2(1280, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_0nqo5"]
atlas = ExtResource("1_u20od")
region = Rect2(1536, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_835d0"]
atlas = ExtResource("1_u20od")
region = Rect2(1792, 256, 256, 256)

[sub_resource type="AtlasTexture" id="AtlasTexture_xjyh0"]
atlas = ExtResource("1_u20od")
region = Rect2(2048, 256, 256, 256)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_ktjth")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_eny82")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_i47jj")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rxqsm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_sfosw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_cfdbf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_r4yvk")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gt80w")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hfod8")
}],
"loop": true,
"name": &"Down",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_2yvwf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_crk0q")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_km2du")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_cj320")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kgbb8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vj2qg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_1knyk")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mf5cg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_oav0o")
}],
"loop": true,
"name": &"Left",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_1irqt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mvs6t")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_au1uo")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ap213")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_q1f5x")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gvgq1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rhlgc")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3rvm4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vcbee")
}],
"loop": true,
"name": &"Right",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_hbpp6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kacpb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_x0og6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_btfxi")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6gue7")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ddkyj")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_0nqo5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_835d0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xjyh0")
}],
"loop": true,
"name": &"Up",
"speed": 10.0
}]
