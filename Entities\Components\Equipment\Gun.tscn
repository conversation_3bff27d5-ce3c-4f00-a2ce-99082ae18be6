[gd_scene load_steps=4 format=3 uid="uid://b7xm2m6j41ym4"]

[ext_resource type="Script" uid="uid://echmfevu1md5" path="res://Entities/Components/Equipment/gun.gd" id="1_w0qsb"]
[ext_resource type="Texture2D" uid="uid://dn2wduaabgsgy" path="res://Assets/IconGodotNode/node_2D/icon_call.png" id="2_ogaxr"]
[ext_resource type="Script" uid="uid://oa3um0l58mkh" path="res://Entities/Components/General/shadow.gd" id="3_54ubg"]

[node name="Gun" type="Node2D"]
script = ExtResource("1_w0qsb")

[node name="ShootTimer" type="Timer" parent="."]

[node name="Sprite2D" type="Sprite2D" parent="."]
position = Vector2(6, -5)
texture = ExtResource("2_ogaxr")

[node name="Shadow2D" type="Sprite2D" parent="Sprite2D"]
modulate = Color(0, 0, 0, 1)
z_index = -1
texture = ExtResource("2_ogaxr")
script = ExtResource("3_54ubg")

[node name="shoot_pos" type="Marker2D" parent="Sprite2D"]
position = Vector2(7, -1)

[connection signal="timeout" from="ShootTimer" to="." method="_on_shoot_timer_timeout"]
